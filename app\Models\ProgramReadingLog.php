<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class ProgramReadingLog extends BaseModel
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'program_reading_logs';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_id',
        'book_id',
        'user_id',
        'program_task_instance_id',
        'reading_date',
        'start_page',
        'end_page',
        'reading_duration_minutes',
        'reading_notes',
        'is_verified',
        'verified_by',
        'verified_at',
        'points_awarded',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'reading_date' => 'date',
            'start_page' => 'integer',
            'end_page' => 'integer',
            'pages_read' => 'integer',
            'reading_duration_minutes' => 'integer',
            'reading_speed_pages_per_minute' => 'decimal:2',
            'is_verified' => 'boolean',
            'verified_at' => 'datetime',
            'points_awarded' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the program this reading log belongs to.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the book being read.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the user (student) who made this log entry.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the task instance if this log is assigned as a task.
     */
    public function taskInstance(): BelongsTo
    {
        return $this->belongsTo(ProgramTaskInstance::class, 'program_task_instance_id');
    }

    /**
     * Get the teacher who verified this log.
     */
    public function verifier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Scope to get verified logs.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to get unverified logs.
     */
    public function scopeUnverified($query)
    {
        return $query->where('is_verified', false);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeBetweenDates($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('reading_date', [$startDate, $endDate]);
    }

    /**
     * Scope to get recent logs.
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('reading_date', '>=', now()->subDays($days));
    }

    /**
     * Scope to filter by user.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeForBook($query, int $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Scope to filter by program.
     */
    public function scopeForProgram($query, int $programId)
    {
        return $query->where('program_id', $programId);
    }

    /**
     * Scope to get logs with task assignments.
     */
    public function scopeWithTasks($query)
    {
        return $query->whereNotNull('program_task_instance_id');
    }

    /**
     * Scope to get logs without task assignments.
     */
    public function scopeWithoutTasks($query)
    {
        return $query->whereNull('program_task_instance_id');
    }

    /**
     * Check if this log entry can be created.
     */
    public static function canCreateEntry(int $programId, int $bookId, int $userId, Carbon $date): array
    {
        // Check if entry already exists for this date
        $existing = static::where('program_id', $programId)
                         ->where('book_id', $bookId)
                         ->where('user_id', $userId)
                         ->where('reading_date', $date->toDateString())
                         ->first();
        
        if ($existing) {
            return ['can_create' => false, 'reason' => 'Entry already exists for this date'];
        }
        
        // Check if date is in the future
        if ($date->isFuture()) {
            return ['can_create' => false, 'reason' => 'Cannot create entries for future dates'];
        }
        
        // Check if book is assigned to user (need to find term_user_id from user_id)
        $termUser = \App\Models\TermUser::where('user_id', $userId)->first();

        if (!$termUser) {
            return ['can_create' => false, 'reason' => 'User not found in any term'];
        }

        $assignment = ProgramUserBook::where('program_id', $programId)
                                    ->where('book_id', $bookId)
                                    ->where('term_user_id', $termUser->id)
                                    ->first();
        
        if (!$assignment) {
            return ['can_create' => false, 'reason' => 'Book not assigned to student'];
        }
        
        return ['can_create' => true, 'reason' => null];
    }

    /**
     * Calculate reading progress for a book.
     */
    public function calculateProgress(): array
    {
        $totalLogs = static::where('program_id', $this->program_id)
                          ->where('book_id', $this->book_id)
                          ->where('user_id', $this->user_id)
                          ->get();
        
        $totalPagesRead = $totalLogs->sum('pages_read');
        $totalSessions = $totalLogs->count();
        $averagePagesPerSession = $totalSessions > 0 ? round($totalPagesRead / $totalSessions, 1) : 0;
        
        // Calculate completion percentage (assuming book has total_pages)
        $completionPercentage = 0;
        if ($this->book && $this->book->total_pages > 0) {
            $highestPageRead = $totalLogs->max('end_page') ?? 0;
            $completionPercentage = round(($highestPageRead / $this->book->total_pages) * 100, 1);
        }
        
        return [
            'total_pages_read' => $totalPagesRead,
            'total_sessions' => $totalSessions,
            'average_pages_per_session' => $averagePagesPerSession,
            'completion_percentage' => $completionPercentage,
            'highest_page_reached' => $totalLogs->max('end_page') ?? 0,
            'reading_streak' => $this->calculateReadingStreak(),
        ];
    }

    /**
     * Calculate reading streak for the user.
     */
    public function calculateReadingStreak(): int
    {
        $logs = static::where('program_id', $this->program_id)
                     ->where('book_id', $this->book_id)
                     ->where('user_id', $this->user_id)
                     ->orderBy('reading_date', 'desc')
                     ->get();
        
        if ($logs->isEmpty()) {
            return 0;
        }
        
        $streak = 0;
        $currentDate = now()->toDateString();
        
        foreach ($logs as $log) {
            $logDate = $log->reading_date->toDateString();
            
            if ($logDate === $currentDate || $logDate === now()->subDay()->toDateString()) {
                $streak++;
                $currentDate = now()->subDays($streak)->toDateString();
            } else {
                break;
            }
        }
        
        return $streak;
    }

    /**
     * Award points for this reading log entry.
     */
    public function awardPoints(): void
    {
        if ($this->points_awarded > 0) {
            return; // Points already awarded
        }
        
        $basePoints = 10; // Base points for daily reading
        $pageBonus = min($this->pages_read * 0.5, 20); // 0.5 points per page, max 20 bonus
        $streakBonus = min($this->calculateReadingStreak() * 2, 30); // 2 points per streak day, max 30
        
        $totalPoints = $basePoints + $pageBonus + $streakBonus;
        
        // Verification bonus
        if ($this->is_verified) {
            $totalPoints += 5;
        }
        
        $this->points_awarded = (int) $totalPoints;
        $this->save();
        
        // Create point record
        ProgramUserPoint::create([
            'program_id' => $this->program_id,
            'term_user_id' => $this->user_id,
            'point_source' => ProgramUserPoint::SOURCE_READING,
            'points' => $this->points_awarded,
            'earned_at' => now(),
        ]);
    }

    /**
     * Verify this reading log entry.
     */
    public function verify(int $verifierId, ?string $notes = null): bool
    {
        if ($this->is_verified) {
            return false; // Already verified
        }
        
        $this->is_verified = true;
        $this->verified_by = $verifierId;
        $this->verified_at = now();
        
        $saved = $this->save();
        
        if ($saved) {
            // Award verification bonus points
            $bonusPoints = 5;
            $this->points_awarded += $bonusPoints;
            $this->save();
            
            // Create additional point record for verification bonus
            ProgramUserPoint::create([
                'program_id' => $this->program_id,
                'term_user_id' => $this->user_id,
                'point_source' => ProgramUserPoint::SOURCE_BONUS,
                'points' => $bonusPoints,
                'earned_at' => now(),
            ]);
            
            // Mark related task as completed if applicable
            if ($this->taskInstance) {
                $this->taskInstance->markCompleted(
                    $verifierId,
                    $notes ?? "Reading log verified: {$this->pages_read} pages",
                    $this->points_awarded
                );
            }
        }
        
        return $saved;
    }

    /**
     * Calculate reading speed if duration is provided.
     */
    protected static function boot()
    {
        parent::boot();
        
        static::saving(function ($log) {
            // Calculate reading speed if duration is provided
            if ($log->reading_duration_minutes && $log->reading_duration_minutes > 0) {
                $log->reading_speed_pages_per_minute = round($log->pages_read / $log->reading_duration_minutes, 2);
            }
            
            // Award points on creation
            if (!$log->exists && $log->points_awarded === 0) {
                $log->awardPoints();
            }
        });
    }

    /**
     * Get reading statistics for a student.
     */
    public static function getStudentStatistics(int $programId, int $userId, ?int $bookId = null): array
    {
        $query = static::where('program_id', $programId)
                      ->where('user_id', $userId);
        
        if ($bookId) {
            $query = $query->where('book_id', $bookId);
        }
        
        $logs = $query->get();
        
        return [
            'total_sessions' => $logs->count(),
            'total_pages_read' => $logs->sum('pages_read'),
            'total_points_earned' => $logs->sum('points_awarded'),
            'verified_sessions' => $logs->where('is_verified', true)->count(),
            'average_pages_per_session' => $logs->count() > 0 ? round($logs->avg('pages_read'), 1) : 0,
            'average_duration_minutes' => $logs->where('reading_duration_minutes', '>', 0)->avg('reading_duration_minutes') ?? 0,
            'current_reading_streak' => $logs->isNotEmpty() ? $logs->first()->calculateReadingStreak() : 0,
            'longest_session_pages' => $logs->max('pages_read') ?? 0,
            'most_recent_session' => $logs->sortByDesc('reading_date')->first()?->reading_date,
        ];
    }

    /**
     * Get reading logs for a specific date range.
     */
    public static function getLogsForDateRange(int $programId, Carbon $startDate, Carbon $endDate, ?int $userId = null): array
    {
        $query = static::where('program_id', $programId)
                      ->betweenDates($startDate, $endDate)
                      ->with(['user', 'book']);
        
        if ($userId) {
            $query = $query->where('user_id', $userId);
        }
        
        return $query->orderBy('reading_date', 'desc')
                    ->orderBy('user_id')
                    ->get()
                    ->map(function($log) {
                        return [
                            'id' => $log->id,
                            'student_name' => $log->user->name,
                            'book_name' => $log->book->name,
                            'reading_date' => $log->reading_date->format('Y-m-d'),
                            'pages_read' => $log->pages_read,
                            'start_page' => $log->start_page,
                            'end_page' => $log->end_page,
                            'duration_minutes' => $log->reading_duration_minutes,
                            'reading_speed' => $log->reading_speed_pages_per_minute,
                            'points_awarded' => $log->points_awarded,
                            'is_verified' => $log->is_verified,
                            'reading_notes' => $log->reading_notes,
                        ];
                    })
                    ->toArray();
    }
}
