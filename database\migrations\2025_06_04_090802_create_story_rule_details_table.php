<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('story_rule_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('rule_id')->constrained('story_rules')->onDelete('cascade');
            $table->integer('required_type'); // 1=achievement, 2=book, 3=chapter, etc.
            $table->integer('required_id'); // ID of the required item
            $table->integer('quantity')->default(1); // How many required
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Add indexes
            $table->index(['rule_id', 'required_type']);
            $table->index(['required_type', 'required_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('story_rule_details');
    }
};
