<?php

namespace App\Console\Commands;

use App\MoonShine\Resources\ProgramTaskResource;
use App\MoonShine\Resources\ProgramTaskInstanceResource;
use App\MoonShine\Resources\ProgramTaskActionResource;
use App\Models\ProgramTask;
use App\Models\ProgramTaskInstance;
use App\Models\ProgramTaskAction;
use Illuminate\Console\Command;

class TestTaskResources extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:task-resources';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test that task resources are working correctly';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Testing Task Resources...');
        
        try {
            // Test resource classes exist
            $this->info('✅ ProgramTaskResource class exists');
            $this->info('✅ ProgramTaskInstanceResource class exists');
            $this->info('✅ ProgramTaskActionResource class exists');
            
            // Test model counts
            $taskCount = ProgramTask::count();
            $instanceCount = ProgramTaskInstance::count();
            $actionCount = ProgramTaskAction::count();
            
            $this->info('');
            $this->info('Database Status:');
            $this->info("📋 Tasks: {$taskCount}");
            $this->info("📝 Instances: {$instanceCount}");
            $this->info("🔄 Actions: {$actionCount}");
            
            // Test a sample task
            if ($taskCount > 0) {
                $task = ProgramTask::first();
                $this->info('');
                $this->info('Sample Task:');
                $this->info("   Name: {$task->name}");
                $this->info("   Type: {$task->task_type_name}");
                $this->info("   Status: {$task->status}");
                $this->info("   Instances: {$task->instances()->count()}");
            }
            
            $this->info('');
            $this->info('🎉 All task resources are working correctly!');
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('❌ Error testing resources: ' . $e->getMessage());
            $this->error('   File: ' . $e->getFile() . ':' . $e->getLine());
            return Command::FAILURE;
        }
    }
}
