<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;
use App\MoonShine\Resources\UserResource;
use Illuminate\Support\Facades\Auth;
use MoonShine\Contracts\Core\DependencyInjection\CoreContract;

class TestUserResourceFiltering extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:user-resource';

    /**
     * The console command description.
     */
    protected $description = 'Test UserResource filtering for different roles';

    /**
     * Execute the console command.
     */
    public function handle(CoreContract $core)
    {
        $this->info('=== UserResource Filtering Test ===');
        $this->newLine();

        // Test System Admin
        $systemAdmin = User::whereHas('role', function($q) {
            $q->where('level', Role::LEVEL_SYSTEM_ADMIN);
        })->first();

        if ($systemAdmin) {
            Auth::guard('moonshine')->login($systemAdmin);
            
            $resource = new UserResource($core);
            $reflection = new \ReflectionClass($resource);
            $method = $reflection->getMethod('resolveQuery');
            $method->setAccessible(true);
            $query = $method->invoke($resource);
            
            $userCount = $query->count();
            $title = $resource->getTitle();
            
            $this->info("System Admin ({$systemAdmin->name}):");
            $this->line("  - Title: {$title}");
            $this->line("  - Can see {$userCount} users (should see all users)");
            
            Auth::guard('moonshine')->logout();
        }

        $this->newLine();

        // Test School Admin
        $schoolAdmin = User::whereHas('role', function($q) {
            $q->where('level', Role::LEVEL_SCHOOL_ADMIN);
        })->first();

        if ($schoolAdmin) {
            Auth::guard('moonshine')->login($schoolAdmin);

            $resource = new UserResource($core);
            $reflection = new \ReflectionClass($resource);
            $method = $reflection->getMethod('resolveQuery');
            $method->setAccessible(true);
            $query = $method->invoke($resource);
            
            $userCount = $query->count();
            $title = $resource->getTitle();
            
            $this->info("School Admin ({$schoolAdmin->name}):");
            $this->line("  - Title: {$title}");
            $this->line("  - Can see {$userCount} users (should see students and teachers)");
            
            Auth::guard('moonshine')->logout();
        }

        $this->newLine();

        // Test Teacher
        $teacher = User::whereHas('role', function($q) {
            $q->where('level', Role::LEVEL_TEACHER);
        })->first();

        if ($teacher) {
            Auth::guard('moonshine')->login($teacher);

            $resource = new UserResource($core);
            $reflection = new \ReflectionClass($resource);
            $method = $reflection->getMethod('resolveQuery');
            $method->setAccessible(true);
            $query = $method->invoke($resource);
            
            $userCount = $query->count();
            $title = $resource->getTitle();
            
            $this->info("Teacher ({$teacher->name}):");
            $this->line("  - Title: {$title}");
            $this->line("  - Can see {$userCount} users (should only see students in their classes)");
            
            // Test authorization methods
            $canCreate = $this->invokePrivateMethod($resource, 'checkCreatePermission', [$teacher]);
            $canUpdate = $this->invokePrivateMethod($resource, 'checkUpdatePermission', [$teacher]);
            $canDelete = $this->invokePrivateMethod($resource, 'checkDeletePermission', [$teacher]);
            
            $this->line("  - Can create: " . ($canCreate ? 'YES' : 'NO'));
            $this->line("  - Can update: " . ($canUpdate ? 'YES' : 'NO'));
            $this->line("  - Can delete: " . ($canDelete ? 'YES' : 'NO'));
            
            Auth::guard('moonshine')->logout();
        }

        $this->newLine();

        // Test Student (should not have access)
        $student = User::whereHas('role', function($q) {
            $q->where('level', Role::LEVEL_STUDENT);
        })->first();

        if ($student) {
            Auth::guard('moonshine')->login($student);

            $resource = new UserResource($core);
            $reflection = new \ReflectionClass($resource);
            $method = $reflection->getMethod('resolveQuery');
            $method->setAccessible(true);
            $query = $method->invoke($resource);
            
            $userCount = $query->count();
            $title = $resource->getTitle();
            
            $this->info("Student ({$student->name}):");
            $this->line("  - Title: {$title}");
            $this->line("  - Can see {$userCount} users (should see 0 - no access)");
            
            Auth::guard('moonshine')->logout();
        }

        $this->newLine();
        $this->info('=== Test Complete ===');
        
        return 0;
    }

    private function invokePrivateMethod($object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        
        return $method->invokeArgs($object, $parameters);
    }
}
