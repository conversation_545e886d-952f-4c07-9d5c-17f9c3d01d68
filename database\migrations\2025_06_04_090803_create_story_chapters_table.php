<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('story_chapters', function (Blueprint $table) {
            $table->id();
            $table->foreignId('story_id')->constrained('stories')->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->integer('sequence'); // Order of chapters
            $table->foreignId('unlock_rule_id')->nullable()->constrained('story_rules')->onDelete('set null');
            $table->integer('map_start_x')->nullable(); // Starting X coordinate on map
            $table->integer('map_start_y')->nullable(); // Starting Y coordinate on map
            $table->integer('map_end_x')->nullable(); // Ending X coordinate on map
            $table->integer('map_end_y')->nullable(); // Ending Y coordinate on map
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Add indexes
            $table->index(['story_id', 'sequence']);
            $table->unique(['story_id', 'sequence']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('story_chapters');
    }
};
