<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('program_task_instances', function (Blueprint $table) {
            $table->id();
            
            // Core assignment information
            $table->foreignId('program_task_id')->constrained('program_tasks')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')
                  ->comment('Assigned student');
            
            // Instance-specific dates
            $table->date('start_date')->comment('When this instance becomes available');
            $table->date('end_date')->comment('Deadline for this specific instance');
            
            // Status tracking
            $table->enum('status', ['pending', 'completed', 'missed', 'excused'])
                  ->default('pending')->comment('Current status');
            
            // Assignment context
            $table->enum('assigned_via', ['individual', 'team'])
                  ->comment('How task was assigned');
            $table->foreignId('team_id')->nullable()->constrained('program_teams')->onDelete('set null')
                  ->comment('If assigned via team');
            
            // Audit fields
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Indexes for performance
            $table->index(['program_task_id', 'user_id']);
            $table->index(['user_id', 'status']);
            $table->index(['start_date', 'end_date']);
            $table->index(['status', 'end_date']);
            $table->index('team_id');
            
            // Unique constraint to prevent duplicate assignments
            $table->unique(['program_task_id', 'user_id', 'start_date'], 'unique_task_user_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('program_task_instances');
    }
};
