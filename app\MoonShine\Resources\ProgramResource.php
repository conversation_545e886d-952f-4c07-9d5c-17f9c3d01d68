<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Program;
use App\Models\Story;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\StoryResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
 
/**
 * @extends BaseResource<Program>
 */
class ProgramResource extends BaseResource
{
    protected string $model = Program::class;

    protected string $column = 'name';

    protected array $with = ['story', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.programs');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            Text::make(__('admin.program_name'), 'name')
                ->sortable(),
            BelongsTo::make(__('admin.story'), 'story', 
                formatted: fn(Story $story) => $story->title)
                ->sortable(),
            Date::make(__('admin.start_date'), 'start_date')
                ->format('d.m.Y')
                ->sortable(),
            Date::make(__('admin.end_date'), 'end_date')
                ->format('d.m.Y')
                ->sortable(),
            Text::make(__('admin.status'), 'status')
                ->badge(fn($status) => match($status) {
                    'active' => 'green',
                    'upcoming' => 'blue',
                    'completed' => 'gray',
                    'inactive' => 'red',
                    default => 'gray'
                }),
            Switcher::make(__('admin.is_active'), 'is_active'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    Text::make(__('admin.program_name'), 'name')
                        ->required()
                        ->placeholder(__('admin.enter_name')),
                ]),
                
                Textarea::make(__('admin.program_description'), 'description')
                    ->placeholder(__('admin.enter_description')),
                
                BelongsTo::make(__('admin.story'), 'story', 
                    formatted: fn(Story $story) => $story->title,
                    resource: StoryResource::class)
                    ->required()
                    ->placeholder(__('admin.select_story')),
                
                Flex::make([
                    Date::make(__('admin.start_date'), 'start_date')
                        ->required(),
                    
                    Date::make(__('admin.end_date'), 'end_date')
                        ->required(),
                ]),
                
                Switcher::make(__('admin.is_active'), 'is_active')
                    ->default(true),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            Text::make(__('admin.program_name'), 'name'),
            Textarea::make(__('admin.program_description'), 'description'),
            BelongsTo::make(__('admin.story'), 'story', 
                formatted: fn(Story $story) => $story->title),
            Date::make(__('admin.start_date'), 'start_date')
                ->format('d.m.Y'),
            Date::make(__('admin.end_date'), 'end_date')
                ->format('d.m.Y'),
            Text::make(__('admin.duration'), 'duration')
                ->badge('blue'),
            Text::make(__('admin.status'), 'status')
                ->badge(fn($status) => match($status) {
                    'active' => 'green',
                    'upcoming' => 'blue',
                    'completed' => 'gray',
                    'inactive' => 'red',
                    default => 'gray'
                }),
            Switcher::make(__('admin.is_active'), 'is_active')
                ->disabled(),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.story'), 'story', 
                formatted: fn(Story $story) => $story->title,
                resource: StoryResource::class),
            Switcher::make(__('admin.is_active'), 'is_active'),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'story_id' => ['required', 'exists:stories,id'],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'is_active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['name', 'description'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

}
