<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Program;
use App\Models\ProgramUserCharacter;
use App\Models\StoryCharacter;
use App\Models\TermUser;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\TermUserResource;
use App\MoonShine\Resources\StoryCharacterResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;

/**
 * @extends BaseResource<ProgramUserCharacter>
 */
class ProgramUserCharacterResource extends BaseResource
{
    protected string $model = ProgramUserCharacter::class;

    protected array $with = ['program', 'termUser', 'storyCharacter', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_user_characters');
    }



    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            BelongsTo::make(__('admin.term_users'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name)
                ->sortable(),
            BelongsTo::make(__('admin.story_characters'), 'storyCharacter', 
                formatted: fn(StoryCharacter $character) => $character->name)
                ->sortable(),
            Number::make(__('admin.current_stage'), 'current_stage')
                ->badge('blue'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.programs'), 'program', 
                    formatted: fn(Program $program) => $program->name,
                    resource: ProgramResource::class)
                    ->required()
                    ->placeholder(__('admin.select_program')),
                
                BelongsTo::make(__('admin.term_users'), 'termUser', 
                    formatted: fn(TermUser $termUser) => $termUser->user->name,
                    resource: TermUserResource::class)
                    ->required()
                    ->placeholder(__('admin.select_student')),
                
                Flex::make([
                    BelongsTo::make(__('admin.story_characters'), 'storyCharacter', 
                        formatted: fn(StoryCharacter $character) => $character->name,
                        resource: StoryCharacterResource::class)
                        ->required()
                        ->placeholder(__('admin.select_character')),
                    
                    Number::make(__('admin.current_stage'), 'current_stage')
                        ->required()
                        ->min(1)
                        ->default(1),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name),
            BelongsTo::make(__('admin.term_users'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name),
            BelongsTo::make(__('admin.story_characters'), 'storyCharacter', 
                formatted: fn(StoryCharacter $character) => $character->name),
            Number::make(__('admin.current_stage'), 'current_stage')
                ->badge('blue'),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
            BelongsTo::make(__('admin.story_characters'), 'storyCharacter', 
                formatted: fn(StoryCharacter $character) => $character->name,
                resource: StoryCharacterResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'term_user_id' => ['required', 'exists:term_users,id'],
            'story_character_id' => ['required', 'exists:story_characters,id'],
            'current_stage' => ['required', 'integer', 'min:1'],
            ...parent::getCommonRules($item),
        ];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

}
