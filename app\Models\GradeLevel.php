<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class GradeLevel extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'created_by',
        'updated_by',
    ];

    /**
     * Get organizations that have this grade level.
     */
    public function organizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'organization_grade_levels');
    }

    /**
     * Get school classes for this grade level.
     */
    public function schoolClasses(): HasMany
    {
        return $this->hasMany(SchoolClass::class);
    }

    /**
     * Scope to order by grade level name numerically.
     */
    public function scopeOrderByGrade($query)
    {
        return $query->orderByRaw('CAST(name AS UNSIGNED), name');
    }

    /**
     * Get numeric value of grade level.
     */
    public function getNumericValueAttribute(): int
    {
        if (is_numeric($this->name)) {
            return (int) $this->name;
        }
        
        // Handle special cases like "Prep", "Kindergarten", etc.
        $specialGrades = [
            'Prep' => 0,
            'Kindergarten' => 0,
            'Pre-K' => -1,
        ];
        
        return $specialGrades[$this->name] ?? 999;
    }

    /**
     * Check if this is a numeric grade.
     */
    public function isNumericGrade(): bool
    {
        return is_numeric($this->name);
    }

    /**
     * Check if this is a special grade (non-numeric).
     */
    public function isSpecialGrade(): bool
    {
        return !$this->isNumericGrade();
    }

    /**
     * Get formatted grade name.
     */
    public function getFormattedNameAttribute(): string
    {
        if ($this->isNumericGrade()) {
            return $this->name . '. Sınıf';
        }
        
        return $this->name;
    }
}
