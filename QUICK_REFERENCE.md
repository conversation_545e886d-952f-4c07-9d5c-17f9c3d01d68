# Quick Reference Guide - Reading Program System

## Model Constants

### ProgramUserPoint Sources
```php
const SOURCE_ACHIEVEMENT = 1;  // Achievement
const SOURCE_TASK = 2;         // Task
const SOURCE_QUEST = 3;        // Quest
const SOURCE_READING = 4;      // Reading
const SOURCE_BONUS = 5;        // Bonus
```

### ProgramUserMap Item Types
```php
const TYPE_ITEM = 1;        // Item
const TYPE_BADGE = 2;       // Badge
const TYPE_REWARD = 3;      // Reward
const TYPE_TROPHY = 4;      // Trophy
const TYPE_COLLECTIBLE = 5; // Collectible
```

### StoryAchievement Types
```php
const TYPE_ITEM = 'item';
const TYPE_BADGE = 'badge';
const TYPE_REWARD = 'reward';
const TYPE_TROPHY = 'trophy';
const TYPE_COLLECTIBLE = 'collectible';
```

## Key Relationships

### Program Model
```php
// Core relationships
$program->story()              // BelongsTo Story
$program->schools()            // BelongsToMany Organization (via program_schools)
$program->classes()            // BelongsToMany SchoolClass (via program_classes)
$program->books()              // BelongsToMany Book (via program_books)

// Progress tracking
$program->teams()              // HasMany ProgramTeam
$program->userLevels()         // HasMany ProgramUserLevel
$program->userAchievements()   // HasMany ProgramUserAchievement
$program->userCharacters()     // HasMany ProgramUserCharacter
$program->userMaps()           // HasMany ProgramUserMap
$program->userPoints()         // HasMany ProgramUserPoint
```

### Program Status Values
```php
'active'    // Currently running and within date range
'inactive'  // Manually deactivated
'upcoming'  // Start date is in the future
'completed' // End date has passed
```

## Database Constraints

### Unique Constraints
- `program_schools`: (program_id, organization_id)
- `program_classes`: (program_id, school_class_id)
- `program_books`: (program_id, book_id)
- `program_team_members`: (program_team_id, term_user_id)
- `program_user_levels`: (program_id, term_user_id)
- `program_user_achievements`: (program_id, term_user_id, story_achievement_id)
- `program_user_characters`: (program_id, term_user_id, story_character_id)
- `program_user_maps`: (program_id, term_user_id, item_type, item_id)

### Foreign Key Relationships
```sql
programs.story_id → stories.id
program_schools.program_id → programs.id
program_schools.organization_id → organizations.id
program_classes.program_id → programs.id
program_classes.school_class_id → school_classes.id
program_books.program_id → programs.id
program_books.book_id → books.id
program_teams.program_id → programs.id
program_team_members.program_team_id → program_teams.id
program_team_members.term_user_id → term_users.id
program_user_levels.program_id → programs.id
program_user_levels.term_user_id → term_users.id
program_user_levels.story_chapter_id → story_chapters.id
program_user_achievements.program_id → programs.id
program_user_achievements.term_user_id → term_users.id
program_user_achievements.story_achievement_id → story_achievements.id
program_user_characters.program_id → programs.id
program_user_characters.term_user_id → term_users.id
program_user_characters.story_character_id → story_characters.id
program_user_maps.program_id → programs.id
program_user_maps.term_user_id → term_users.id
program_user_points.program_id → programs.id
program_user_points.term_user_id → term_users.id
```

## Validation Rules

### Program
```php
'name' => 'required|string|max:255'
'description' => 'nullable|string'
'story_id' => 'required|exists:stories,id'
'start_date' => 'required|date'
'end_date' => 'required|date|after:start_date'
'is_active' => 'boolean'
```

### ProgramUserPoint
```php
'program_id' => 'required|exists:programs,id'
'term_user_id' => 'required|exists:term_users,id'
'point_source' => 'required|integer|min:1|max:5'
'points' => 'required|integer|min:1'
'earned_at' => 'required|date'
```

### ProgramUserMap
```php
'program_id' => 'required|exists:programs,id'
'term_user_id' => 'required|exists:term_users,id'
'item_type' => 'required|integer|min:1|max:5'
'item_id' => 'required|integer|min:1'
'x_coordinate' => 'required|numeric'
'y_coordinate' => 'required|numeric'
```

## Common Scopes

### Program Scopes
```php
Program::active()              // Where is_active = true
Program::current()             // Within start_date and end_date
```

### ProgramUserAchievement Scopes
```php
ProgramUserAchievement::recent($days)     // Earned within last X days
ProgramUserAchievement::byType($type)     // Filter by achievement type
```

### ProgramUserPoint Scopes
```php
ProgramUserPoint::bySource($source)      // Filter by point source
ProgramUserPoint::recent($days)          // Earned within last X days
ProgramUserPoint::betweenDates($start, $end)  // Within date range
```

### ProgramUserMap Scopes
```php
ProgramUserMap::byType($type)            // Filter by item type
ProgramUserMap::withinRange($minX, $maxX, $minY, $maxY)  // Within coordinates
```

## Useful Accessors

### Program
```php
$program->is_current           // Boolean: active and within date range
$program->duration             // Integer: duration in days
$program->status               // String: active|inactive|upcoming|completed
```

### ProgramTeam
```php
$team->member_count            // Integer: number of team members
```

### ProgramUserCharacter
```php
$character->current_stage_details  // Get current stage information
$character->canEvolve()           // Boolean: can evolve to next stage
$character->evolve()              // Boolean: evolve to next stage
```

### ProgramUserPoint
```php
$point->point_source_display_name  // String: human-readable source name
```

### ProgramUserMap
```php
$map->item_type_display_name      // String: human-readable item type
```

## Admin Menu Structure
```
Reading Programs/
├── Programs
├── Program Schools
├── Program Classes
├── Program Books
├── Program Teams
├── Program Team Members
├── Program User Levels
├── Program User Achievements
├── Program User Characters
├── Program User Maps
└── Program User Points
```

## Translation Keys
All translation keys use dot notation under `admin.*`:
- `admin.reading_programs`
- `admin.programs`
- `admin.program_schools`
- `admin.program_name`
- `admin.start_date`
- `admin.end_date`
- `admin.is_active`
- `admin.status`
- `admin.points`
- `admin.point_source`
- etc.

This reference guide provides quick access to the most commonly used constants, relationships, and validation rules in the reading program system.
