@extends('layouts.teacher')

@section('title', __('teacher.dashboard_title'))

@section('content')
<div class="teacher-container">
    <!-- Header Card -->
    <x-moonshine::card class="mb-6 card-touch">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">
                    {{ __('teacher.welcome_message', ['name' => $user->name]) }}
                </h1>
                <p class="text-gray-600 mt-1">
                    {{ __('teacher.dashboard_subtitle') }}
                </p>
            </div>
            <div class="flex-shrink-0">
                <div class="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center">
                    <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </x-moonshine::card>

    <!-- Quick Stats Grid -->
    <x-moonshine::layout.grid class="mb-6">
        <x-moonshine::layout.column colSpan="6" adaptiveColSpan="12">
            <x-moonshine::card class="card-touch">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">{{ __('teacher.my_students') }}</p>
                        <p class="text-lg font-semibold text-gray-900">--</p>
                    </div>
                </div>
            </x-moonshine::card>
        </x-moonshine::layout.column>

        <x-moonshine::layout.column colSpan="6" adaptiveColSpan="12">
            <x-moonshine::card class="card-touch">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500">{{ __('teacher.active_programs') }}</p>
                        <p class="text-lg font-semibold text-gray-900">--</p>
                    </div>
                </div>
            </x-moonshine::card>
        </x-moonshine::layout.column>
    </x-moonshine::layout.grid>

    <!-- Quick Actions Card -->
    <x-moonshine::card class="mb-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">
            {{ __('teacher.quick_actions') }}
        </h2>
        <div class="grid grid-cols-1 gap-3">
            <x-moonshine::link-button 
                href="#" 
                class="btn-touch bg-purple-50 text-purple-700 border border-purple-200 hover:bg-purple-100 flex items-center justify-between"
                onclick="showToast('{{ __('teacher.coming_soon') }}')"
            >
                <div class="flex items-center">
                    <svg class="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <span>{{ __('teacher.view_students') }}</span>
                </div>
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </x-moonshine::link-button>

            <x-moonshine::link-button 
                href="#" 
                class="btn-touch bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100 flex items-center justify-between"
                onclick="showToast('{{ __('teacher.coming_soon') }}')"
            >
                <div class="flex items-center">
                    <svg class="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                    <span>{{ __('teacher.assign_tasks') }}</span>
                </div>
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </x-moonshine::link-button>

            <x-moonshine::link-button 
                href="#" 
                class="btn-touch bg-green-50 text-green-700 border border-green-200 hover:bg-green-100 flex items-center justify-between"
                onclick="showToast('{{ __('teacher.coming_soon') }}')"
            >
                <div class="flex items-center">
                    <svg class="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span>{{ __('teacher.view_reports') }}</span>
                </div>
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </x-moonshine::link-button>
        </div>
    </x-moonshine::card>

    <!-- Recent Activity Card -->
    <x-moonshine::card class="mb-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">
            {{ __('teacher.recent_activity') }}
        </h2>
        <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">{{ __('teacher.no_activity') }}</h3>
            <p class="mt-1 text-sm text-gray-500">{{ __('teacher.no_activity_description') }}</p>
        </div>
    </x-moonshine::card>

    <!-- Coming Soon Notice Card -->
    <x-moonshine::card class="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-purple-800">
                    {{ __('teacher.coming_soon') }}
                </h3>
                <div class="mt-2 text-sm text-purple-700">
                    <p>{{ __('teacher.coming_soon_description') }}</p>
                </div>
            </div>
        </div>
    </x-moonshine::card>
</div>
@endsection
