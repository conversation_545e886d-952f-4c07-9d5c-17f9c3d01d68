<?php return array (
  'laravel/pail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Pail\\PailServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => '<PERSON>vel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'moonshine/advanced' => 
  array (
    'providers' => 
    array (
      0 => 'MoonShine\\Advanced\\Providers\\AdvancedServiceProvider',
    ),
  ),
  'moonshine/jwt' => 
  array (
    'providers' => 
    array (
      0 => 'MoonShine\\JWT\\Providers\\JWTServiceProvider',
    ),
  ),
  'moonshine/moonshine' => 
  array (
    'providers' => 
    array (
      0 => 'MoonShine\\Laravel\\Providers\\MoonShineServiceProvider',
    ),
  ),
  'moonshine/oag' => 
  array (
    'providers' => 
    array (
      0 => 'MoonShine\\OAG\\Providers\\OAGServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'pestphp/pest-plugin-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Pest\\Laravel\\PestServiceProvider',
    ),
  ),
  'spatie/laravel-permission' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Permission\\PermissionServiceProvider',
    ),
  ),
  'sweet1s/moonshine-roles-permissions' => 
  array (
    'providers' => 
    array (
      0 => 'Sweet1s\\MoonshineRBAC\\Providers\\MoonShineRBACServiceProvider',
    ),
  ),
);