<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class BookWord extends BaseModel
{
    use SoftDeletes;

    /**
     * Difficulty level constants.
     */
    const DIFFICULTY_EASY = 'easy';
    const DIFFICULTY_MEDIUM = 'medium';
    const DIFFICULTY_HARD = 'hard';

    /**
     * The table associated with the model.
     */
    protected $table = 'book_words';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'book_id',
        'word',
        'definition',
        'synonym',
        'antonym',
        'page_reference',
        'difficulty_level',
        'is_active',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'page_reference' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get all difficulty levels.
     */
    public static function getDifficultyLevels(): array
    {
        return [
            self::DIFFICULTY_EASY => 'Easy',
            self::DIFFICULTY_MEDIUM => 'Medium',
            self::DIFFICULTY_HARD => 'Hard',
        ];
    }

    /**
     * Get difficulty level name.
     */
    public function getDifficultyLevelNameAttribute(): string
    {
        return self::getDifficultyLevels()[$this->difficulty_level] ?? 'Unknown';
    }

    /**
     * Get the book this word belongs to.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Scope to get active words.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by difficulty level.
     */
    public function scopeOfDifficulty($query, string $difficulty)
    {
        return $query->where('difficulty_level', $difficulty);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeForBook($query, int $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Scope to search by word.
     */
    public function scopeSearchWord($query, string $searchTerm)
    {
        return $query->where('word', 'like', "%{$searchTerm}%");
    }

    /**
     * Scope to filter by page reference.
     */
    public function scopeOnPage($query, int $page)
    {
        return $query->where('page_reference', $page);
    }

    /**
     * Scope to filter by page range.
     */
    public function scopeInPageRange($query, int $startPage, int $endPage)
    {
        return $query->whereBetween('page_reference', [$startPage, $endPage]);
    }

    /**
     * Check if word has definition.
     */
    public function hasDefinition(): bool
    {
        return !empty($this->definition);
    }

    /**
     * Check if word has synonym.
     */
    public function hasSynonym(): bool
    {
        return !empty($this->synonym);
    }

    /**
     * Check if word has antonym.
     */
    public function hasAntonym(): bool
    {
        return !empty($this->antonym);
    }

    /**
     * Get word display with page reference.
     */
    public function getWordDisplayAttribute(): string
    {
        $display = $this->word;
        if ($this->page_reference) {
            $display .= " (p. {$this->page_reference})";
        }
        return $display;
    }

    /**
     * Get formatted word information.
     */
    public function getFormattedInfoAttribute(): array
    {
        return [
            'word' => $this->word,
            'definition' => $this->definition,
            'synonym' => $this->synonym,
            'antonym' => $this->antonym,
            'page' => $this->page_reference,
            'difficulty' => $this->difficulty_level_name,
        ];
    }

    /**
     * Generate vocabulary activity questions.
     */
    public function generateVocabularyQuestions(): array
    {
        $questions = [];
        
        // Definition question
        if ($this->hasDefinition()) {
            $questions[] = [
                'type' => 'definition',
                'question' => "What does '{$this->word}' mean?",
                'correct_answer' => $this->definition,
                'word_id' => $this->id,
            ];
        }
        
        // Synonym question
        if ($this->hasSynonym()) {
            $questions[] = [
                'type' => 'synonym',
                'question' => "What is a synonym for '{$this->word}'?",
                'correct_answer' => $this->synonym,
                'word_id' => $this->id,
            ];
        }
        
        // Antonym question
        if ($this->hasAntonym()) {
            $questions[] = [
                'type' => 'antonym',
                'question' => "What is an antonym for '{$this->word}'?",
                'correct_answer' => $this->antonym,
                'word_id' => $this->id,
            ];
        }
        
        return $questions;
    }

    /**
     * Get random words for vocabulary activities.
     */
    public static function getRandomWordsForActivity(int $bookId, int $count, ?string $difficulty = null, ?int $pageStart = null, ?int $pageEnd = null): array
    {
        $query = static::active()->forBook($bookId);
        
        if ($difficulty) {
            $query = $query->ofDifficulty($difficulty);
        }
        
        if ($pageStart && $pageEnd) {
            $query = $query->inPageRange($pageStart, $pageEnd);
        }
        
        return $query->inRandomOrder()
                    ->limit($count)
                    ->get()
                    ->toArray();
    }

    /**
     * Get words by difficulty distribution.
     */
    public static function getWordsByDifficulty(int $bookId): array
    {
        $words = static::active()->forBook($bookId)->get();
        
        return [
            'easy' => $words->where('difficulty_level', self::DIFFICULTY_EASY)->count(),
            'medium' => $words->where('difficulty_level', self::DIFFICULTY_MEDIUM)->count(),
            'hard' => $words->where('difficulty_level', self::DIFFICULTY_HARD)->count(),
            'total' => $words->count(),
        ];
    }

    /**
     * Search words with their related information.
     */
    public static function searchWithDetails(string $searchTerm, ?int $bookId = null): array
    {
        $query = static::active()
                      ->where(function($q) use ($searchTerm) {
                          $q->where('word', 'like', "%{$searchTerm}%")
                            ->orWhere('definition', 'like', "%{$searchTerm}%")
                            ->orWhere('synonym', 'like', "%{$searchTerm}%")
                            ->orWhere('antonym', 'like', "%{$searchTerm}%");
                      });
        
        if ($bookId) {
            $query = $query->forBook($bookId);
        }
        
        return $query->with('book')
                    ->orderBy('word')
                    ->get()
                    ->map(function($word) {
                        return [
                            'id' => $word->id,
                            'word' => $word->word,
                            'definition' => $word->definition,
                            'synonym' => $word->synonym,
                            'antonym' => $word->antonym,
                            'page_reference' => $word->page_reference,
                            'difficulty_level' => $word->difficulty_level_name,
                            'book_name' => $word->book->name,
                            'word_display' => $word->word_display,
                        ];
                    })
                    ->toArray();
    }
}
