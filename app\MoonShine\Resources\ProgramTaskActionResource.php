<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ProgramTaskAction;
use App\Models\ProgramTaskInstance;
use App\Models\User;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Layout\Box;

// Import related resources
use App\MoonShine\Resources\ProgramTaskInstanceResource;
use App\MoonShine\Resources\UserResource;

/**
 * @extends BaseResource<ProgramTaskAction>
 */
#[Icon('clipboard-document')]
class ProgramTaskActionResource extends BaseResource
{
    protected string $model = ProgramTaskAction::class;

    protected array $with = ['programTaskInstance', 'programTaskInstance.programTask', 'programTaskInstance.user', 'completedBy', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_task_actions');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.task_name'), 'programTaskInstance.programTask.name')
                ->sortable(),
            
            Text::make(__('admin.student'), 'programTaskInstance.user.name')
                ->sortable(),
            /*
            Text::make(__('admin.action_type'), 'action_type_name')
                ->badge(fn($item) => $item->action_badge_color)
                ->sortable(),
            */
            Date::make(__('admin.action_date'), 'action_date')
                ->withTime()
                ->format('d.m.Y H:i')
                ->sortable(),
            
            Number::make(__('admin.points_awarded'), 'points_awarded')
                ->badge('green'),
            
            Text::make(__('admin.completed_by'), 'completedBy.name'),
            
            Text::make(__('admin.action_summary'), 'action_summary'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.task_instance'), 'programTaskInstance', 
                    formatted: fn(ProgramTaskInstance $instance) => 
                        $instance->programTask->name . ' - ' . $instance->user->name,
                    resource: ProgramTaskInstanceResource::class)
                    ->required()
                    ->placeholder(__('admin.select_task_instance')),
                
                Flex::make([
                    Select::make(__('admin.action_type'), 'action_type')
                        ->options(ProgramTaskAction::getActionTypes())
                        ->required()
                        ->placeholder(__('admin.select_action_type')),
                    
                    Date::make(__('admin.action_date'), 'action_date')
                        ->withTime()
                        ->format('d.m.Y H:i')
                        ->required()
                        ->default(now()),
                ]),
                
                Textarea::make(__('admin.notes'), 'notes')
                    ->placeholder(__('admin.enter_notes')),
                
                Flex::make([
                    Number::make(__('admin.points_awarded'), 'points_awarded')
                        ->min(0)
                        ->placeholder(__('admin.enter_points_awarded')),
                    
                    BelongsTo::make(__('admin.completed_by'), 'completedBy', 
                        formatted: fn(User $user) => $user->name,
                        resource: UserResource::class)
                        ->nullable()
                        ->placeholder(__('admin.select_completed_by')),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.task_name'), 'programTaskInstance.programTask.name'),
            Text::make(__('admin.program_name'), 'programTaskInstance.programTask.program.name'),
            Text::make(__('admin.student'), 'programTaskInstance.user.name'),
            Text::make(__('admin.action_type'), 'action_type_name')
                ->badge(fn($item) => $item->action_badge_color),
            Date::make(__('admin.action_date'), 'action_date')
                ->withTime()
                ->format('d.m.Y H:i'),
            Textarea::make(__('admin.notes'), 'notes'),
            Number::make(__('admin.points_awarded'), 'points_awarded')
                ->badge('green'),
            BelongsTo::make(__('admin.completed_by'), 'completedBy', 
                formatted: fn(User $user) => $user->name),
            Text::make(__('admin.action_summary'), 'action_summary')
                ->badge('blue'),
            ...parent::getCommonDetailFields(),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.task_instance'), 'programTaskInstance', 
                formatted: fn(ProgramTaskInstance $instance) => 
                    $instance->programTask->name . ' - ' . $instance->user->name,
                resource: ProgramTaskInstanceResource::class),
            Select::make(__('admin.action_type'), 'action_type')
                ->options(ProgramTaskAction::getActionTypes()),
            BelongsTo::make(__('admin.completed_by'), 'completedBy', 
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_task_instance_id' => ['required', 'exists:program_task_instances,id'],
            'action_type' => ['required', 'in:completed,missed,excused,reassigned'],
            'action_date' => ['required', 'date'],
            'notes' => ['nullable', 'string'],
            'points_awarded' => ['nullable', 'integer', 'min:0'],
            'completed_by' => ['nullable', 'exists:users,id'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['programTaskInstance.programTask.name', 'programTaskInstance.user.name', 'notes'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }
}
