<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\BookWord;
use App\Models\Book;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Layout\Box;

// Import related resources
use App\MoonShine\Resources\BookResource;

/**
 * @extends BaseResource<BookWord>
 */
#[Icon('book-open')]
class BookWordResource extends BaseResource
{
    protected string $model = BookWord::class;

    protected array $with = ['book', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.book_words');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name)
                ->sortable(),
            
            Text::make(__('admin.word'), 'word')
                ->sortable(),
            
            Text::make(__('admin.definition'), 'definition'),
            
            Text::make(__('admin.synonym'), 'synonym')
                ->badge('blue'),
            
            Text::make(__('admin.antonym'), 'antonym')
                ->badge('orange'),
            
            Number::make(__('admin.page_reference'), 'page_reference')
                ->badge('gray'),
            
            Select::make(__('admin.difficulty_level'), 'difficulty_level')
                ->options(BookWord::getDifficultyLevels())
                ->sortable(),
            
            Switcher::make(__('admin.is_active'), 'is_active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.books'), 'book', 
                    formatted: fn(Book $book) => $book->name,
                    resource: BookResource::class)
                    ->required()
                    ->placeholder(__('admin.select_book')),
                
                Flex::make([
                    Text::make(__('admin.word'), 'word')
                        ->required()
                        ->placeholder(__('admin.enter_word')),
                    
                    Number::make(__('admin.page_reference'), 'page_reference')
                        ->min(1)
                        ->placeholder(__('admin.enter_page_reference')),
                ]),
                
                Textarea::make(__('admin.definition'), 'definition')
                    ->placeholder(__('admin.enter_definition')),
                
                Flex::make([
                    Text::make(__('admin.synonym'), 'synonym')
                        ->placeholder(__('admin.enter_synonym')),
                    
                    Text::make(__('admin.antonym'), 'antonym')
                        ->placeholder(__('admin.enter_antonym')),
                ]),
                
                Flex::make([
                    Select::make(__('admin.difficulty_level'), 'difficulty_level')
                        ->options(BookWord::getDifficultyLevels())
                        ->required()
                        ->default('medium'),
                    
                    Switcher::make(__('admin.is_active'), 'is_active')
                        ->default(true),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.word'), 'word'),
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name),
            Textarea::make(__('admin.definition'), 'definition'),
            Text::make(__('admin.synonym'), 'synonym')
                ->badge('blue'),
            Text::make(__('admin.antonym'), 'antonym')
                ->badge('orange'),
            Number::make(__('admin.page_reference'), 'page_reference')
                ->badge('gray'),
            Text::make(__('admin.difficulty_level'), 'difficulty_level_name')
                ->badge('purple'),
            Text::make(__('admin.word_display'), 'word_display')
                ->badge('green'),
            Switcher::make(__('admin.is_active'), 'is_active')
                ->disabled(),
            ...parent::getCommonDetailFields(),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name,
                resource: BookResource::class),
            Select::make(__('admin.difficulty_level'), 'difficulty_level')
                ->options(BookWord::getDifficultyLevels()),
            Switcher::make(__('admin.is_active'), 'is_active'),
        ];
    }

    public function rules(mixed $item): array
    {
        $bookId = $item->book_id ?? request('book_id');
        
        return [
            'book_id' => ['required', 'exists:books,id'],
            'word' => [
                'required', 
                'string', 
                'max:255',
                "unique:book_words,word,{$item->id},id,book_id,{$bookId}"
            ],
            'definition' => ['nullable', 'string'],
            'synonym' => ['nullable', 'string', 'max:255'],
            'antonym' => ['nullable', 'string', 'max:255'],
            'page_reference' => ['nullable', 'integer', 'min:1'],
            'difficulty_level' => ['required', 'in:easy,medium,hard'],
            'is_active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['word', 'definition', 'synonym', 'antonym'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }
}
