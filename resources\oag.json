{"openapi": "3.0.1", "info": {"title": "MoonShine", "description": "", "version": "1.0.0"}, "servers": [{"url": "http://localhost/moonaug/admin", "description": "Production API server"}], "paths": {"/authenticate": {"post": {"tags": ["Authentication"], "summary": "User login", "operationId": "authenticate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "***"}}}}}}, "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/resource/role-resource/crud": {"get": {"tags": ["roleResource"], "summary": "Roller - Listing", "operationId": "roleResourceIndex", "parameters": [{"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "enum": ["id", "-id", "name", "-name", "description", "-description", "level", "-level", "users_count", "-users_count"]}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1}, "example": 1}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleCollection"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "post": {"tags": ["roleResource"], "summary": "Roller - Create", "operationId": "roleResourceCreate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "level": {"type": "string"}, "description": {"type": "string"}}}}}}, "responses": {"201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["roleResource"], "summary": "Roller - Mass delete", "operationId": "roleResourceMassDelete", "parameters": [{"name": "ids", "in": "query", "required": true, "schema": [{"type": "array"}]}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/role-resource/crud/{resourceItem}": {"put": {"tags": ["roleResource"], "summary": "Roller - Update", "operationId": "roleResourceUpdate", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "level": {"type": "string"}, "description": {"type": "string"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "get": {"tags": ["roleResource"], "summary": "Roller - Show", "operationId": "roleResourceShow", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["roleResource"], "summary": "Roller - Delete", "operationId": "roleResourceDelete", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/user-resource/crud": {"get": {"tags": ["userResource"], "summary": "Kullanıcılar - Listing", "operationId": "userResourceIndex", "parameters": [{"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "enum": ["id", "-id", "name", "-name", "email", "-email", "role_id", "-role_id", "role.level", "-role.level"]}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1}, "example": 1}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCollection"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "post": {"tags": ["userResource"], "summary": "<PERSON><PERSON><PERSON><PERSON>ılar - Create", "operationId": "userResourceCreate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "role_id": {"type": "integer"}, "password": {"type": "string", "format": "password"}, "password_confirmation": {"type": "string", "format": "password"}}}}}}, "responses": {"201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["userResource"], "summary": "Kullanıcılar - Mass delete", "operationId": "userResourceMassDelete", "parameters": [{"name": "ids", "in": "query", "required": true, "schema": [{"type": "array"}]}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/user-resource/crud/{resourceItem}": {"put": {"tags": ["userResource"], "summary": "Kullanıcılar - Update", "operationId": "userResourceUpdate", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "role_id": {"type": "integer"}, "password": {"type": "string", "format": "password"}, "password_confirmation": {"type": "string", "format": "password"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "get": {"tags": ["userResource"], "summary": "Kullanıcılar - Show", "operationId": "userResourceShow", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["userResource"], "summary": "Kullanıcılar - Delete", "operationId": "userResourceDelete", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/term-resource/crud": {"get": {"tags": ["termResource"], "summary": "<PERSON><PERSON><PERSON>mler - Listing", "operationId": "termResourceIndex", "parameters": [{"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "enum": ["id", "-id", "name", "-name", "start_date", "-start_date", "end_date", "-end_date", "active", "-active", "status", "-status"]}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1}, "example": 1}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TermCollection"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "post": {"tags": ["termResource"], "summary": "<PERSON><PERSON><PERSON>mler - Create", "operationId": "termResourceCreate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "start_date": {"type": "string"}, "end_date": {"type": "string"}, "active": {"type": "string"}}}}}}, "responses": {"201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["termResource"], "summary": "Dönemler - Mass delete", "operationId": "termResourceMassDelete", "parameters": [{"name": "ids", "in": "query", "required": true, "schema": [{"type": "array"}]}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/term-resource/crud/{resourceItem}": {"put": {"tags": ["termResource"], "summary": "Dönemler - Update", "operationId": "termResourceUpdate", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "start_date": {"type": "string"}, "end_date": {"type": "string"}, "active": {"type": "string"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "get": {"tags": ["termResource"], "summary": "<PERSON><PERSON><PERSON><PERSON> - Show", "operationId": "termResourceShow", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Term"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["termResource"], "summary": "Dönemler - Delete", "operationId": "termResourceDelete", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/organization-resource/crud": {"get": {"tags": ["organizationResource"], "summary": "Kurumlar - Listing", "operationId": "organizationResourceIndex", "parameters": [{"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "enum": ["id", "-id", "name", "-name", "org_type", "-org_type", "parent_id", "-parent_id", "active", "-active"]}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1}, "example": 1}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationCollection"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "post": {"tags": ["organizationResource"], "summary": "<PERSON><PERSON><PERSON> - Create", "operationId": "organizationResourceCreate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "org_type": {"type": "string"}, "parent_id": {"type": "integer"}, "active": {"type": "string", "default": true}, "gradeLevels": {"type": "string"}}}}}}, "responses": {"201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["organizationResource"], "summary": "Kurumlar - Mass delete", "operationId": "organizationResourceMassDelete", "parameters": [{"name": "ids", "in": "query", "required": true, "schema": [{"type": "array"}]}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/organization-resource/crud/{resourceItem}": {"put": {"tags": ["organizationResource"], "summary": "Kurumlar - Update", "operationId": "organizationResourceUpdate", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "org_type": {"type": "string"}, "parent_id": {"type": "integer"}, "active": {"type": "string", "default": true}, "gradeLevels": {"type": "string"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "get": {"tags": ["organizationResource"], "summary": "<PERSON><PERSON><PERSON> - Show", "operationId": "organizationResourceShow", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["organizationResource"], "summary": "Kurumlar - Delete", "operationId": "organizationResourceDelete", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/grade-level-resource/crud": {"get": {"tags": ["gradeLevelResource"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Listing", "operationId": "gradeLevelResourceIndex", "parameters": [{"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "enum": ["id", "-id", "name", "-name"]}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1}, "example": 1}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GradeLevelCollection"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "post": {"tags": ["gradeLevelResource"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Create", "operationId": "gradeLevelResourceCreate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}}}}}}, "responses": {"201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["gradeLevelResource"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Mass delete", "operationId": "gradeLevelResourceMassDelete", "parameters": [{"name": "ids", "in": "query", "required": true, "schema": [{"type": "array"}]}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/grade-level-resource/crud/{resourceItem}": {"put": {"tags": ["gradeLevelResource"], "summary": "Sınıf <PERSON> - Update", "operationId": "gradeLevelResourceUpdate", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "get": {"tags": ["gradeLevelResource"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Show", "operationId": "gradeLevelResourceShow", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GradeLevel"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["gradeLevelResource"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Delete", "operationId": "gradeLevelResourceDelete", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/school-class-resource/crud": {"get": {"tags": ["schoolClassResource"], "summary": "Sınıflar - Listing", "operationId": "schoolClassResourceIndex", "parameters": [{"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "enum": ["id", "-id", "name", "-name", "organization_id", "-organization_id", "grade_level_id", "-grade_level_id"]}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1}, "example": 1}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchoolClassCollection"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "post": {"tags": ["schoolClassResource"], "summary": "Sınıflar - Create", "operationId": "schoolClassResourceCreate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "organization_id": {"type": "integer"}, "grade_level_id": {"type": "integer"}}}}}}, "responses": {"201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["schoolClassResource"], "summary": "Sınıflar - Mass delete", "operationId": "schoolClassResourceMassDelete", "parameters": [{"name": "ids", "in": "query", "required": true, "schema": [{"type": "array"}]}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/school-class-resource/crud/{resourceItem}": {"put": {"tags": ["schoolClassResource"], "summary": "Sınıflar - Update", "operationId": "schoolClassResourceUpdate", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "organization_id": {"type": "integer"}, "grade_level_id": {"type": "integer"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "get": {"tags": ["schoolClassResource"], "summary": "Sınıflar - Show", "operationId": "schoolClassResourceShow", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchoolClass"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["schoolClassResource"], "summary": "Sınıflar - Delete", "operationId": "schoolClassResourceDelete", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/term-user-resource/crud": {"get": {"tags": ["termUserResource"], "summary": "Dönem Atamaları - Listing", "operationId": "termUserResourceIndex", "parameters": [{"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "enum": ["id", "-id", "user_id", "-user_id", "role_id", "-role_id", "organization_id", "-organization_id", "school_class_id", "-school_class_id", "term_id", "-term_id", "active", "-active"]}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1}, "example": 1}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TermUserCollection"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "post": {"tags": ["termUserResource"], "summary": "Dönem Atamaları - Create", "operationId": "termUserResourceCreate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "integer"}, "role_id": {"type": "integer"}, "organization_id": {"type": "integer"}, "school_class_id": {"type": "integer"}, "term_id": {"type": "integer"}, "active": {"type": "string", "default": true}}}}}}, "responses": {"201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["termUserResource"], "summary": "Dönem Atamaları - Mass delete", "operationId": "termUserResourceMassDelete", "parameters": [{"name": "ids", "in": "query", "required": true, "schema": [{"type": "array"}]}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/term-user-resource/crud/{resourceItem}": {"put": {"tags": ["termUserResource"], "summary": "Dönem Atamaları - Update", "operationId": "termUserResourceUpdate", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "integer"}, "role_id": {"type": "integer"}, "organization_id": {"type": "integer"}, "school_class_id": {"type": "integer"}, "term_id": {"type": "integer"}, "active": {"type": "string", "default": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "get": {"tags": ["termUserResource"], "summary": "Dönem Atamaları - Show", "operationId": "termUserResourceShow", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TermUser"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["termUserResource"], "summary": "Dönem Atamaları - Delete", "operationId": "termUserResourceDelete", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/author-resource/crud": {"get": {"tags": ["authorResource"], "summary": "Yazarlar - Listing", "operationId": "authorResourceIndex", "parameters": [{"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "enum": ["id", "-id", "name", "-name"]}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1}, "example": 1}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorCollection"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "post": {"tags": ["authorResource"], "summary": "<PERSON><PERSON>lar - Create", "operationId": "authorResourceCreate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}}}}}}, "responses": {"201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["authorResource"], "summary": "Yazarlar - Mass delete", "operationId": "authorResourceMassDelete", "parameters": [{"name": "ids", "in": "query", "required": true, "schema": [{"type": "array"}]}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/author-resource/crud/{resourceItem}": {"put": {"tags": ["authorResource"], "summary": "Yazarlar - Update", "operationId": "authorResourceUpdate", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "get": {"tags": ["authorResource"], "summary": "<PERSON><PERSON><PERSON> - Show", "operationId": "authorResourceShow", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Author"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["authorResource"], "summary": "Yazarlar - Delete", "operationId": "authorResourceDelete", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/publisher-resource/crud": {"get": {"tags": ["publisherResource"], "summary": "Yayınevleri - Listing", "operationId": "publisherResourceIndex", "parameters": [{"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "enum": ["id", "-id", "name", "-name"]}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1}, "example": 1}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublisherCollection"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "post": {"tags": ["publisherResource"], "summary": "Yayınevleri - Create", "operationId": "publisherResourceCreate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}}}}}}, "responses": {"201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["publisherResource"], "summary": "Yayınevleri - Mass delete", "operationId": "publisherResourceMassDelete", "parameters": [{"name": "ids", "in": "query", "required": true, "schema": [{"type": "array"}]}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/publisher-resource/crud/{resourceItem}": {"put": {"tags": ["publisherResource"], "summary": "Yayınevleri - Update", "operationId": "publisherResourceUpdate", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "get": {"tags": ["publisherResource"], "summary": "Yayınevleri - Show", "operationId": "publisherResourceShow", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Publisher"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["publisherResource"], "summary": "Yayınevleri - Delete", "operationId": "publisherResourceDelete", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/book-resource/crud": {"get": {"tags": ["bookResource"], "summary": "Kitaplar - Listing", "operationId": "bookResourceIndex", "parameters": [{"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "enum": ["id", "-id", "name", "-name", "isbn", "-isbn", "publisher_id", "-publisher_id", "page_count", "-page_count", "year_of_publish", "-year_of_publish"]}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1}, "example": 1}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookCollection"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "post": {"tags": ["bookResource"], "summary": "Kitaplar - Create", "operationId": "bookResourceCreate", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "isbn": {"type": "string"}, "publisher_id": {"type": "integer"}, "page_count": {"type": "integer"}, "year_of_publish": {"type": "integer"}, "authors": {"type": "string"}}}}}}, "responses": {"201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["bookResource"], "summary": "Kitaplar - Mass delete", "operationId": "bookResourceMassDelete", "parameters": [{"name": "ids", "in": "query", "required": true, "schema": [{"type": "array"}]}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}, "/resource/book-resource/crud/{resourceItem}": {"put": {"tags": ["bookResource"], "summary": "Kitaplar - Update", "operationId": "bookResourceUpdate", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "isbn": {"type": "string"}, "publisher_id": {"type": "integer"}, "page_count": {"type": "integer"}, "year_of_publish": {"type": "integer"}, "authors": {"type": "string"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}, "get": {"tags": ["bookResource"], "summary": "Kitaplar - Show", "operationId": "bookResourceShow", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"description": "Successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Book"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"auth:api": []}]}, "delete": {"tags": ["bookResource"], "summary": "Kitaplar - Delete", "operationId": "bookResourceDelete", "parameters": [{"name": "resourceItem", "in": "path", "required": true, "schema": {"oneOf": [{"type": "integer"}, {"type": "string"}]}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "201": {"$ref": "#/components/responses/Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "422": {"$ref": "#/components/responses/ValidationException"}}, "security": [{"auth:api": []}]}}}, "components": {"securitySchemes": {"auth:api": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"Role": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "level": {"type": "string"}, "users_count": {"type": "string"}}}, "RoleCollection": {"type": "object", "properties": {"current_page": {"type": "integer", "default": 1}, "first_page_url": {"type": "string"}, "from": {"type": "integer", "default": 1}, "next_page_url": {"type": "string", "nullable": true}, "prev_page_url": {"type": "string", "nullable": true}, "to": {"type": "integer"}, "path": {"type": "string"}, "per_page": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}}}, "User": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "role_id": {"type": "integer"}, "role.level": {"type": "string"}}}, "UserCollection": {"type": "object", "properties": {"current_page": {"type": "integer", "default": 1}, "first_page_url": {"type": "string"}, "from": {"type": "integer", "default": 1}, "next_page_url": {"type": "string", "nullable": true}, "prev_page_url": {"type": "string", "nullable": true}, "to": {"type": "integer"}, "path": {"type": "string"}, "per_page": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}, "Term": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "start_date": {"type": "string"}, "end_date": {"type": "string"}, "active": {"type": "string"}, "status": {"type": "string"}}}, "TermCollection": {"type": "object", "properties": {"current_page": {"type": "integer", "default": 1}, "first_page_url": {"type": "string"}, "from": {"type": "integer", "default": 1}, "next_page_url": {"type": "string", "nullable": true}, "prev_page_url": {"type": "string", "nullable": true}, "to": {"type": "integer"}, "path": {"type": "string"}, "per_page": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Term"}}}}, "Organization": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "org_type": {"type": "string"}, "parent_id": {"type": "integer"}, "active": {"type": "string"}}}, "OrganizationCollection": {"type": "object", "properties": {"current_page": {"type": "integer", "default": 1}, "first_page_url": {"type": "string"}, "from": {"type": "integer", "default": 1}, "next_page_url": {"type": "string", "nullable": true}, "prev_page_url": {"type": "string", "nullable": true}, "to": {"type": "integer"}, "path": {"type": "string"}, "per_page": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}}, "GradeLevel": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "formatted_name": {"type": "string"}}}, "GradeLevelCollection": {"type": "object", "properties": {"current_page": {"type": "integer", "default": 1}, "first_page_url": {"type": "string"}, "from": {"type": "integer", "default": 1}, "next_page_url": {"type": "string", "nullable": true}, "prev_page_url": {"type": "string", "nullable": true}, "to": {"type": "integer"}, "path": {"type": "string"}, "per_page": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GradeLevel"}}}}, "SchoolClass": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "organization_id": {"type": "integer"}, "grade_level_id": {"type": "integer"}, "student_count": {"type": "integer"}}}, "SchoolClassCollection": {"type": "object", "properties": {"current_page": {"type": "integer", "default": 1}, "first_page_url": {"type": "string"}, "from": {"type": "integer", "default": 1}, "next_page_url": {"type": "string", "nullable": true}, "prev_page_url": {"type": "string", "nullable": true}, "to": {"type": "integer"}, "path": {"type": "string"}, "per_page": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SchoolClass"}}}}, "TermUser": {"type": "object", "properties": {"id": {"type": "string"}, "user_id": {"type": "integer"}, "role_id": {"type": "integer"}, "organization_id": {"type": "integer"}, "school_class_id": {"type": "integer"}, "term_id": {"type": "integer"}, "active": {"type": "string"}}}, "TermUserCollection": {"type": "object", "properties": {"current_page": {"type": "integer", "default": 1}, "first_page_url": {"type": "string"}, "from": {"type": "integer", "default": 1}, "next_page_url": {"type": "string", "nullable": true}, "prev_page_url": {"type": "string", "nullable": true}, "to": {"type": "integer"}, "path": {"type": "string"}, "per_page": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TermUser"}}}}, "Author": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "book_count": {"type": "integer"}}}, "AuthorCollection": {"type": "object", "properties": {"current_page": {"type": "integer", "default": 1}, "first_page_url": {"type": "string"}, "from": {"type": "integer", "default": 1}, "next_page_url": {"type": "string", "nullable": true}, "prev_page_url": {"type": "string", "nullable": true}, "to": {"type": "integer"}, "path": {"type": "string"}, "per_page": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Author"}}}}, "Publisher": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "book_count": {"type": "integer"}}}, "PublisherCollection": {"type": "object", "properties": {"current_page": {"type": "integer", "default": 1}, "first_page_url": {"type": "string"}, "from": {"type": "integer", "default": 1}, "next_page_url": {"type": "string", "nullable": true}, "prev_page_url": {"type": "string", "nullable": true}, "to": {"type": "integer"}, "path": {"type": "string"}, "per_page": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Publisher"}}}}, "Book": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "isbn": {"type": "string"}, "publisher_id": {"type": "integer"}, "author_names": {"type": "string"}, "page_count": {"type": "integer"}, "year_of_publish": {"type": "integer"}}}, "BookCollection": {"type": "object", "properties": {"current_page": {"type": "integer", "default": 1}, "first_page_url": {"type": "string"}, "from": {"type": "integer", "default": 1}, "next_page_url": {"type": "string", "nullable": true}, "prev_page_url": {"type": "string", "nullable": true}, "to": {"type": "integer"}, "path": {"type": "string"}, "per_page": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Book"}}}}}, "responses": {"Unauthorized": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "Success": {"description": "Successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "ValidationException": {"description": "Validation errors", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "tags": [{"name": "Authentication"}, {"name": "roleResource"}, {"name": "userResource"}, {"name": "termResource"}, {"name": "organizationResource"}, {"name": "gradeLevelResource"}, {"name": "schoolClassResource"}, {"name": "termUserResource"}, {"name": "authorResource"}, {"name": "publisherResource"}, {"name": "bookResource"}]}