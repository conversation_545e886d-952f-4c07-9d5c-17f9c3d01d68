<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProgramUserMap extends BaseModel
{
    use SoftDeletes;

    /**
     * Item type constants.
     */
    const TYPE_ITEM = 1;
    const TYPE_BADGE = 2;
    const TYPE_REWARD = 3;
    const TYPE_TROPHY = 4;
    const TYPE_COLLECTIBLE = 5;

    /**
     * The table associated with the model.
     */
    protected $table = 'program_user_maps';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_id',
        'term_user_id',
        'item_type',
        'item_id',
        'x_coordinate',
        'y_coordinate',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'item_type' => 'integer',
            'item_id' => 'integer',
            'x_coordinate' => 'decimal:2',
            'y_coordinate' => 'decimal:2',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get all item types.
     */
    public static function getItemTypes(): array
    {
        return [
            self::TYPE_ITEM => 'Item',
            self::TYPE_BADGE => 'Badge',
            self::TYPE_REWARD => 'Reward',
            self::TYPE_TROPHY => 'Trophy',
            self::TYPE_COLLECTIBLE => 'Collectible',
        ];
    }

    /**
     * Get the program.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the term user (student).
     */
    public function termUser(): BelongsTo
    {
        return $this->belongsTo(TermUser::class);
    }

    /**
     * Get item type display name.
     */
    public function getItemTypeDisplayNameAttribute(): string
    {
        return self::getItemTypes()[$this->item_type] ?? 'Unknown';
    }

    /**
     * Scope to get items by type.
     */
    public function scopeByType($query, int $type)
    {
        return $query->where('item_type', $type);
    }

    /**
     * Scope to get items within coordinate range.
     */
    public function scopeWithinRange($query, float $minX, float $maxX, float $minY, float $maxY)
    {
        return $query->whereBetween('x_coordinate', [$minX, $maxX])
                    ->whereBetween('y_coordinate', [$minY, $maxY]);
    }
}
