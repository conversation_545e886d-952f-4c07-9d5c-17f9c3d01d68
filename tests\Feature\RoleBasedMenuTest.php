<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Term;
use App\Models\TermUser;
use App\Models\Organization;
use App\Models\SchoolClass;
use App\MoonShine\Layouts\MoonShineLayout;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;

class RoleBasedMenuTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'System Admin', 'level' => Role::LEVEL_SYSTEM_ADMIN, 'description' => 'System Administrator']);
        Role::create(['name' => 'School Admin', 'level' => Role::LEVEL_SCHOOL_ADMIN, 'description' => 'School Administrator']);
        Role::create(['name' => 'Teacher', 'level' => Role::LEVEL_TEACHER, 'description' => 'Teacher']);
        Role::create(['name' => 'Student', 'level' => Role::LEVEL_STUDENT, 'description' => 'Student']);
    }

    public function test_system_admin_sees_all_menus()
    {
        $systemAdminRole = Role::where('level', Role::LEVEL_SYSTEM_ADMIN)->first();
        $user = User::factory()->create(['role_id' => $systemAdminRole->id]);
        
        Auth::guard('moonshine')->login($user);
        
        $layout = new MoonShineLayout();
        $menu = $this->invokePrivateMethod($layout, 'menu');
        
        // System admin should see system menu
        $this->assertContainsMenuGroup($menu, 'admin.system');
        $this->assertContainsMenuItem($menu, 'admin.school_classes');
        $this->assertContainsMenuItem($menu, 'admin.users');
        $this->assertNotContainsMenuItem($menu, 'admin.my_students');
    }

    public function test_school_admin_sees_limited_menus()
    {
        $schoolAdminRole = Role::where('level', Role::LEVEL_SCHOOL_ADMIN)->first();
        $user = User::factory()->create(['role_id' => $schoolAdminRole->id]);
        
        Auth::guard('moonshine')->login($user);
        
        $layout = new MoonShineLayout();
        $menu = $this->invokePrivateMethod($layout, 'menu');
        
        // School admin should NOT see system menu
        $this->assertNotContainsMenuGroup($menu, 'admin.system');
        $this->assertContainsMenuItem($menu, 'admin.school_classes');
        $this->assertContainsMenuItem($menu, 'admin.users');
        $this->assertNotContainsMenuItem($menu, 'admin.my_students');
    }

    public function test_teacher_sees_my_students_menu()
    {
        $teacherRole = Role::where('level', Role::LEVEL_TEACHER)->first();
        $user = User::factory()->create(['role_id' => $teacherRole->id]);
        
        Auth::guard('moonshine')->login($user);
        
        $layout = new MoonShineLayout();
        $menu = $this->invokePrivateMethod($layout, 'menu');
        
        // Teacher should NOT see system menu or school classes
        $this->assertNotContainsMenuGroup($menu, 'admin.system');
        $this->assertNotContainsMenuItem($menu, 'admin.school_classes');
        $this->assertNotContainsMenuItem($menu, 'admin.users');
        $this->assertContainsMenuItem($menu, 'admin.my_students');
    }

    public function test_unauthenticated_user_gets_empty_menu()
    {
        // No user authenticated
        $layout = new MoonShineLayout();
        $menu = $this->invokePrivateMethod($layout, 'menu');
        
        // Should return empty menu
        $this->assertEmpty($menu);
    }

    private function invokePrivateMethod($object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        
        return $method->invokeArgs($object, $parameters);
    }

    private function assertContainsMenuGroup(array $menu, string $groupKey)
    {
        $found = false;
        foreach ($menu as $item) {
            if (method_exists($item, 'getLabel') && $item->getLabel() === __($groupKey)) {
                $found = true;
                break;
            }
        }
        $this->assertTrue($found, "Menu group '{$groupKey}' not found in menu");
    }

    private function assertNotContainsMenuGroup(array $menu, string $groupKey)
    {
        $found = false;
        foreach ($menu as $item) {
            if (method_exists($item, 'getLabel') && $item->getLabel() === __($groupKey)) {
                $found = true;
                break;
            }
        }
        $this->assertFalse($found, "Menu group '{$groupKey}' should not be in menu");
    }

    private function assertContainsMenuItem(array $menu, string $itemKey)
    {
        $found = false;
        foreach ($menu as $item) {
            if (method_exists($item, 'getLabel') && $item->getLabel() === __($itemKey)) {
                $found = true;
                break;
            }
            // Check if it's a menu group with items
            if (method_exists($item, 'getItems')) {
                foreach ($item->getItems() as $subItem) {
                    if (method_exists($subItem, 'getLabel') && $subItem->getLabel() === __($itemKey)) {
                        $found = true;
                        break 2;
                    }
                }
            }
        }
        $this->assertTrue($found, "Menu item '{$itemKey}' not found in menu");
    }

    private function assertNotContainsMenuItem(array $menu, string $itemKey)
    {
        $found = false;
        foreach ($menu as $item) {
            if (method_exists($item, 'getLabel') && $item->getLabel() === __($itemKey)) {
                $found = true;
                break;
            }
            // Check if it's a menu group with items
            if (method_exists($item, 'getItems')) {
                foreach ($item->getItems() as $subItem) {
                    if (method_exists($subItem, 'getLabel') && $subItem->getLabel() === __($itemKey)) {
                        $found = true;
                        break 2;
                    }
                }
            }
        }
        $this->assertFalse($found, "Menu item '{$itemKey}' should not be in menu");
    }
}
