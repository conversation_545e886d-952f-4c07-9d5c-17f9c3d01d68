.advanced-button-group{display:flex}.advanced-button-group .btn{position:relative;border-radius:0}.advanced-button-group .btn:first-child{border-top-left-radius:var(--radius-md, .375rem);border-bottom-left-radius:var(--radius-md, .375rem)}.advanced-button-group .btn:last-child{border-top-right-radius:var(--radius-md, .375rem);border-bottom-right-radius:var(--radius-md, .375rem)}.advanced-button-group .btn input{display:none}.advanced-button-group .btn label{position:absolute;top:0;right:0;bottom:0;left:0;cursor:pointer}.advanced-button-group .btn+.btn{margin-inline-start:-1px}.advanced-button-group .btn:has(:checked){--tw-border-opacity: 1;border-color:rgba(var(--primary),var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgba(var(--primary),var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.advanced-button-group .btn:has(:checked):hover{background-color:rgba(var(--primary),.8);--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.advanced-button-group .btn:has(:checked):focus{--tw-ring-color: rgba(var(--primary), var(--tw-ring-opacity, 1));--tw-ring-opacity: .2}.advanced-button-group .btn:has(:checked):focus:is(.dark *){--tw-ring-opacity: .4}.js-stepper-head-container{display:flex;flex-wrap:wrap;align-items:center;justify-content:flex-start;gap:1rem}.js-stepper-head-container .js-stepper-head{display:flex;align-items:center}.js-stepper-head-container .js-stepper-head:not(:last-child):after{margin-inline-start:1rem;height:1.5rem;width:1.5rem;--tw-bg-opacity: 1;background-color:rgb(75 85 99 / var(--tw-bg-opacity, 1))}.js-stepper-head-container .js-stepper-head:not(:last-child):is(.dark *):after{--tw-bg-opacity: 1;background-color:rgb(156 163 175 / var(--tw-bg-opacity, 1))}.js-stepper-head-container .js-stepper-head:not(:last-child):after{content:"";-webkit-mask:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%232e2e2e' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");mask:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%232e2e2e' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");-webkit-mask-size:cover;mask-size:cover}.js-stepper-head-container .js-stepper-head .btn{min-width:2.75rem}.js-stepper-head-container .js-stepper-head .js-stepper-head-state-done .btn{--tw-border-opacity: 1;border-color:rgba(var(--primary),var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgba(var(--primary),var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.js-stepper-head-container .js-stepper-head .js-stepper-head-state-done .btn:hover{background-color:rgba(var(--primary),.8);--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.js-stepper-head-container .js-stepper-head .js-stepper-head-state-done .btn:focus{--tw-ring-color: rgba(var(--primary), var(--tw-ring-opacity, 1));--tw-ring-opacity: .2}.js-stepper-head-container .js-stepper-head .js-stepper-head-state-done .btn:focus:is(.dark *){--tw-ring-opacity: .4}.js-stepper-head-container .js-stepper-head .js-stepper-head-state-done .btn span{display:none}.js-stepper-head-container .js-stepper-head .js-stepper-head-state-done .btn:before{height:1.25rem;width:1.25rem;--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));content:"";-webkit-mask:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' aria-hidden='true' data-slot='icon'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='m4.5 12.75 6 6 9-13.5'%3E%3C/path%3E%3C/svg%3E");mask:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' aria-hidden='true' data-slot='icon'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='m4.5 12.75 6 6 9-13.5'%3E%3C/path%3E%3C/svg%3E");-webkit-mask-size:cover;mask-size:cover}.js-stepper-head-container .js-stepper-head .js-stepper-head-state-active .badge{display:flex;height:2.5rem;min-width:2.75rem;align-items:center;justify-content:center}.js-stepper-head-container .js-stepper-head .js-stepper-head-state-active .js-stepper-head-description{font-size:.875rem;line-height:1.5em;--tw-text-opacity: 1;color:rgb(148 163 184 / var(--tw-text-opacity, 1))}.js-stepper-head-container .js-stepper-head .js-stepper-head-state-active .js-stepper-head-description:is(.dark *){--tw-text-opacity: 1;color:rgb(100 116 139 / var(--tw-text-opacity, 1))}.js-stepper-content>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.link-group{display:flex;flex-direction:column}.link-group>:not([hidden])~:not([hidden]){--tw-divide-y-reverse: 0;border-top-width:calc(1px * calc(1 - var(--tw-divide-y-reverse)));border-bottom-width:calc(1px * var(--tw-divide-y-reverse))}.link-group{overflow:hidden;border-radius:var(--radius-md, .375rem);border-width:1px}.link-group:is(.dark *)>:not([hidden])~:not([hidden]){--tw-divide-opacity: 1;border-color:rgba(var(--dark-200),var(--tw-divide-opacity, 1))}.link-group:is(.dark *){--tw-border-opacity: 1;border-color:rgba(var(--dark-200),var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgba(var(--dark-500),var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(203 213 225 / var(--tw-text-opacity, 1))}.link-group-item{display:flex;align-items:center;justify-content:space-between;padding:1rem;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.35s}.link-group-item:hover{--tw-text-opacity: 1;color:rgba(var(--secondary),var(--tw-text-opacity, 1))}.link-group-item:hover:is(.dark *){--tw-text-opacity: 1;color:rgba(var(--secondary),var(--tw-text-opacity, 1))}.link-group-description{font-size:.875rem;line-height:1.5em;--tw-text-opacity: 1;color:rgb(148 163 184 / var(--tw-text-opacity, 1))}.link-group-description:is(.dark *){--tw-text-opacity: 1;color:rgb(100 116 139 / var(--tw-text-opacity, 1))}
