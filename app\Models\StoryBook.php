<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class StoryBook extends BaseModel
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'story_books';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'story_id',
        'book_id',
        'sequence',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'sequence' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the story this book belongs to.
     */
    public function story(): BelongsTo
    {
        return $this->belongsTo(Story::class);
    }

    /**
     * Get the book.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Scope to get books ordered by sequence.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sequence');
    }

    /**
     * Scope to get books for a specific story.
     */
    public function scopeForStory($query, int $storyId)
    {
        return $query->where('story_id', $storyId);
    }

    /**
     * Get the next sequence number for a story.
     */
    public static function getNextSequence(int $storyId): int
    {
        return static::where('story_id', $storyId)->max('sequence') + 1;
    }

    /**
     * Get books in sequence order for a story.
     */
    public static function getBooksForStory(int $storyId)
    {
        return static::with('book')
                    ->where('story_id', $storyId)
                    ->orderBy('sequence')
                    ->get();
    }

    /**
     * Check if this is the first book in the sequence.
     */
    public function getIsFirstAttribute(): bool
    {
        return $this->sequence === static::where('story_id', $this->story_id)->min('sequence');
    }

    /**
     * Check if this is the last book in the sequence.
     */
    public function getIsLastAttribute(): bool
    {
        return $this->sequence === static::where('story_id', $this->story_id)->max('sequence');
    }

    /**
     * Get the previous book in sequence.
     */
    public function getPreviousBook()
    {
        return static::where('story_id', $this->story_id)
                    ->where('sequence', '<', $this->sequence)
                    ->orderBy('sequence', 'desc')
                    ->first();
    }

    /**
     * Get the next book in sequence.
     */
    public function getNextBook()
    {
        return static::where('story_id', $this->story_id)
                    ->where('sequence', '>', $this->sequence)
                    ->orderBy('sequence')
                    ->first();
    }

    /**
     * Move this book up in sequence (decrease sequence number).
     */
    public function moveUp(): bool
    {
        $previousBook = $this->getPreviousBook();
        
        if (!$previousBook) {
            return false; // Already at the top
        }

        // Swap sequences
        $tempSequence = $this->sequence;
        $this->sequence = $previousBook->sequence;
        $previousBook->sequence = $tempSequence;

        return $this->save() && $previousBook->save();
    }

    /**
     * Move this book down in sequence (increase sequence number).
     */
    public function moveDown(): bool
    {
        $nextBook = $this->getNextBook();
        
        if (!$nextBook) {
            return false; // Already at the bottom
        }

        // Swap sequences
        $tempSequence = $this->sequence;
        $this->sequence = $nextBook->sequence;
        $nextBook->sequence = $tempSequence;

        return $this->save() && $nextBook->save();
    }

    /**
     * Boot method to handle automatic sequence assignment.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($storyBook) {
            if (is_null($storyBook->sequence)) {
                $storyBook->sequence = static::getNextSequence($storyBook->story_id);
            }
        });
    }
}
