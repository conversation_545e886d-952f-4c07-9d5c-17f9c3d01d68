<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Program;
use App\Models\ProgramUserMap;
use App\Models\TermUser;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\TermUserResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;

/**
 * @extends BaseResource<ProgramUserMap>
 */
class ProgramUserMapResource extends BaseResource
{
    protected string $model = ProgramUserMap::class;

    protected array $with = ['program', 'termUser', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_user_maps');
    }



    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            BelongsTo::make(__('admin.term_users'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name)
                ->sortable(),
            Text::make(__('admin.item_type'), 'item_type_display_name')
                ->badge('blue'),
            Number::make(__('admin.item_id'), 'item_id'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.programs'), 'program', 
                    formatted: fn(Program $program) => $program->name,
                    resource: ProgramResource::class)
                    ->required()
                    ->placeholder(__('admin.select_program')),
                
                BelongsTo::make(__('admin.term_users'), 'termUser', 
                    formatted: fn(TermUser $termUser) => $termUser->user->name,
                    resource: TermUserResource::class)
                    ->required()
                    ->placeholder(__('admin.select_student')),
                
                Flex::make([
                    Select::make(__('admin.item_type'), 'item_type')
                        ->required()
                        ->options(ProgramUserMap::getItemTypes()),
                    
                    Number::make(__('admin.item_id'), 'item_id')
                        ->required()
                        ->min(1),
                ]),
                
                Flex::make([
                    Number::make(__('admin.x_coordinate'), 'x_coordinate')
                        ->required()
                        ->step(0.01),
                    
                    Number::make(__('admin.y_coordinate'), 'y_coordinate')
                        ->required()
                        ->step(0.01),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name),
            BelongsTo::make(__('admin.term_users'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name),
            Text::make(__('admin.item_type'), 'item_type_display_name')
                ->badge('blue'),
            Number::make(__('admin.item_id'), 'item_id'),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
            Select::make(__('admin.item_type'), 'item_type')
                ->options(ProgramUserMap::getItemTypes()),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'term_user_id' => ['required', 'exists:term_users,id'],
            'item_type' => ['required', 'integer', 'min:1', 'max:5'],
            'item_id' => ['required', 'integer', 'min:1'],
            'x_coordinate' => ['required', 'numeric'],
            'y_coordinate' => ['required', 'numeric'],
            ...parent::getCommonRules($item),
        ];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

}
