<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['parent_id']);
            
            // Drop the parent_id column
            $table->dropColumn('parent_id');
            
            // Drop the old unique constraint that included parent_id
            $table->dropUnique(['name', 'org_type', 'parent_id']);
            
            // Add new unique constraint without parent_id
            $table->unique(['name', 'org_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            // Add back parent_id column
            $table->foreignId('parent_id')->nullable()->constrained('organizations')->onDelete('cascade');
            
            // Add back the index
            $table->index('parent_id');
            
            // Drop the new unique constraint
            $table->dropUnique(['name', 'org_type']);
            
            // Add back the old unique constraint
            $table->unique(['name', 'org_type', 'parent_id']);
        });
    }
};
