<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Organization;
use App\Models\Program;
use App\Models\ProgramSchool;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\OrganizationResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Components\Layout\Box;

/**
 * @extends BaseResource<ProgramSchool>
 */
class ProgramSchoolResource extends BaseResource
{
    protected string $model = ProgramSchool::class;

    protected array $with = ['program', 'organization', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_schools');
    }



    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            BelongsTo::make(__('admin.organizations'), 'organization', 
                formatted: fn(Organization $organization) => $organization->name)
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.programs'), 'program', 
                    formatted: fn(Program $program) => $program->name,
                    resource: ProgramResource::class)
                    ->required()
                    ->placeholder(__('admin.select_program')),
                
                BelongsTo::make(__('admin.organizations'), 'organization', 
                    formatted: fn(Organization $organization) => $organization->name,
                    resource: OrganizationResource::class)
                    ->required()
                    ->placeholder(__('admin.select_school'))
                    ->asyncSearch('name'),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name),
            BelongsTo::make(__('admin.organizations'), 'organization', 
                formatted: fn(Organization $organization) => $organization->name),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
            BelongsTo::make(__('admin.organizations'), 'organization', 
                formatted: fn(Organization $organization) => $organization->name,
                resource: OrganizationResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'organization_id' => ['required', 'exists:organizations,id'],
            ...parent::getCommonRules($item),
        ];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

}
