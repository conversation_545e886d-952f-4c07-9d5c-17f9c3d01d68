<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('story_character_stages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('character_id')->constrained('story_characters')->onDelete('cascade');
            $table->integer('stage_number'); // Progression level (starts at 1)
            $table->string('name'); // Name of this character stage
            $table->string('image'); // Path to character image at this stage
            $table->foreignId('unlock_rule_id')->nullable()->constrained('story_rules')->onDelete('set null');
            $table->json('abilities')->nullable(); // Optional special abilities
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Add indexes
            $table->index(['character_id', 'stage_number']);
            $table->unique(['character_id', 'stage_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('story_character_stages');
    }
};
