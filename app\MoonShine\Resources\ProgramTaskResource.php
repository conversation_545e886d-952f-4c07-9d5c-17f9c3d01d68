<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ProgramTask;
use App\Models\Program;
use App\Models\Book;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;

// Import related resources
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\BookResource;

/**
 * @extends BaseResource<ProgramTask>
 */
#[Icon('clipboard-document-list')]
class ProgramTaskResource extends BaseResource
{
    protected string $model = ProgramTask::class;

    protected array $with = ['program', 'book', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_tasks');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.task_name'), 'name')
                ->sortable(),
            
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            
            Select::make(__('admin.task_type'), 'task_type')
                ->options(ProgramTask::getTaskTypes())
                ->sortable(),
            
            Switcher::make(__('admin.is_recurring'), 'is_recurring')
                ->sortable(),
            
            Date::make(__('admin.start_date'), 'start_date')
                ->format('d.m.Y')
                ->sortable(),
            
            Date::make(__('admin.end_date'), 'end_date')
                ->format('d.m.Y')
                ->sortable(),
            
            Number::make(__('admin.points'), 'points'),
            
            Text::make(__('admin.status'), 'status')
                ->badge(fn($status) => match($status) {
                    'active' => 'green',
                    'upcoming' => 'blue',
                    'expired' => 'gray',
                    default => 'gray'
                }),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        Flex::make([
                            Text::make(__('admin.task_name'), 'name')
                                ->required()
                                ->placeholder(__('admin.enter_task_name')),
                        ]),
                        
                        Textarea::make(__('admin.task_description'), 'description')
                            ->placeholder(__('admin.enter_task_description')),
                        
                        BelongsTo::make(__('admin.programs'), 'program', 
                            formatted: fn(Program $program) => $program->name,
                            resource: ProgramResource::class)
                            ->required()
                            ->placeholder(__('admin.select_program')),
                        
                        Flex::make([
                            Select::make(__('admin.task_type'), 'task_type')
                                ->options(ProgramTask::getTaskTypes())
                                ->required()
                                ->placeholder(__('admin.select_task_type')),
                            
                            Number::make(__('admin.points'), 'points')
                                ->min(0)
                                ->placeholder(__('admin.enter_points')),
                        ]),
                    ]),
                    
                    Tab::make(__('admin.scheduling'), [
                        Flex::make([
                            Date::make(__('admin.start_date'), 'start_date')
                                ->required(),
                            
                            Date::make(__('admin.end_date'), 'end_date')
                                ->required(),
                        ]),
                        
                        Switcher::make(__('admin.is_recurring'), 'is_recurring')
                            ->default(false),
                        
                        Select::make(__('admin.recurrence_pattern'), 'recurrence_pattern')
                            ->options(ProgramTask::getRecurrencePatterns())
                            ->nullable()
                            ->placeholder(__('admin.select_recurrence_pattern'))
                            ->showWhen('is_recurring', true),
                    ]),
                    
                    Tab::make(__('admin.book_settings'), [
                        BelongsTo::make(__('admin.books'), 'book', 
                            formatted: fn(Book $book) => $book->name,
                            resource: BookResource::class)
                            ->nullable()
                            ->placeholder(__('admin.select_book')),
                        
                        Flex::make([
                            Number::make(__('admin.page_start'), 'page_start')
                                ->min(1)
                                ->placeholder(__('admin.enter_page_start')),
                            
                            Number::make(__('admin.page_end'), 'page_end')
                                ->min(1)
                                ->placeholder(__('admin.enter_page_end')),
                        ]),
                    ]),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.task_name'), 'name'),
            Textarea::make(__('admin.task_description'), 'description'),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name),
            Text::make(__('admin.task_type'), 'task_type_name')
                ->badge('blue'),
            Switcher::make(__('admin.is_recurring'), 'is_recurring')
                ->disabled(),
            Text::make(__('admin.recurrence_pattern'), 'recurrence_pattern_name')
                ->badge('purple'),
            Date::make(__('admin.start_date'), 'start_date')
                ->format('d.m.Y'),
            Date::make(__('admin.end_date'), 'end_date')
                ->format('d.m.Y'),
            Number::make(__('admin.duration'), 'duration')
                ->badge('gray'),
            Number::make(__('admin.points'), 'points')
                ->badge('green'),
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name),
            Text::make(__('admin.page_range'), 'page_range')
                ->badge('yellow'),
            Text::make(__('admin.status'), 'status')
                ->badge(fn($status) => match($status) {
                    'active' => 'green',
                    'upcoming' => 'blue',
                    'expired' => 'gray',
                    default => 'gray'
                }),
            ...parent::getCommonDetailFields(),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
            Select::make(__('admin.task_type'), 'task_type')
                ->options(ProgramTask::getTaskTypes()),
            Switcher::make(__('admin.is_recurring'), 'is_recurring'),
            Select::make(__('admin.recurrence_pattern'), 'recurrence_pattern')
                ->options(ProgramTask::getRecurrencePatterns()),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'task_type' => ['required', 'in:reading_log,activity,question,physical'],
            'is_recurring' => ['boolean'],
            'recurrence_pattern' => ['nullable', 'in:daily,weekly'],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'points' => ['nullable', 'integer', 'min:0'],
            'book_id' => ['nullable', 'exists:books,id'],
            'page_start' => ['nullable', 'integer', 'min:1'],
            'page_end' => ['nullable', 'integer', 'min:1', 'gte:page_start'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['name', 'description'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }
}
