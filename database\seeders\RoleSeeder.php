<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'System Administrator',
                'description' => 'Full system access and management',
            ],
            [
                'name' => 'School Administrator',
                'description' => 'Manages a single school',
            ],
            [
                'name' => 'Teacher',
                'description' => 'Teaches classes and manages students',
            ],
            [
                'name' => 'Student',
                'description' => 'Participates in reading activities',
            ],
        ];

        foreach ($roles as $roleData) {
            Role::firstOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );
        }
    }
}
