<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProgramTeamMember extends BaseModel
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'program_team_members';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_team_id',
        'term_user_id',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the program team.
     */
    public function programTeam(): BelongsTo
    {
        return $this->belongsTo(ProgramTeam::class);
    }

    /**
     * Get the term user (student).
     */
    public function termUser(): BelongsTo
    {
        return $this->belongsTo(TermUser::class);
    }
}
