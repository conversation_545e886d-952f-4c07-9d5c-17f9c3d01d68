<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ProgramTaskInstance;
use App\Models\ProgramTask;
use App\Models\User;
use App\Models\ProgramTeam;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Layout\Box;

// Import related resources
use App\MoonShine\Resources\ProgramTaskResource;
use App\MoonShine\Resources\UserResource;
use App\MoonShine\Resources\ProgramTeamResource;

/**
 * @extends BaseResource<ProgramTaskInstance>
 */
#[Icon('clipboard-document-check')]
class ProgramTaskInstanceResource extends BaseResource
{
    protected string $model = ProgramTaskInstance::class;

    protected array $with = ['programTask', 'programTask.program', 'user', 'team', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_task_instances');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.task_name'), 'programTask.name')
                ->sortable(),
            
            Text::make(__('admin.program_name'), 'programTask.program.name')
                ->sortable(),
            
            BelongsTo::make(__('admin.student'), 'user', 
                formatted: fn(User $user) => $user->name)
                ->sortable(),
            
            Date::make(__('admin.start_date'), 'start_date')
                ->format('d.m.Y')
                ->sortable(),
            
            Date::make(__('admin.end_date'), 'end_date')
                ->format('d.m.Y')
                ->sortable(),
            /*
            Text::make(__('admin.status'), 'status_name')
                ->badge(fn($item) => match($item->status) {
                    'pending' => 'blue',
                    'completed' => 'green',
                    'missed' => 'red',
                    'excused' => 'yellow',
                    default => 'gray'
                })
                ->sortable(),
            */
            Number::make(__('admin.days_remaining'), 'days_remaining')
                ->badge(fn($value) => $value <= 0 ? 'red' : ($value <= 3 ? 'yellow' : 'green')),
            
            Text::make(__('admin.assigned_via'), 'assignment_type_name')
                ->badge('purple'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.program_tasks'), 'programTask', 
                    formatted: fn(ProgramTask $task) => $task->name,
                    resource: ProgramTaskResource::class)
                    ->required()
                    ->placeholder(__('admin.select_task')),
                
                BelongsTo::make(__('admin.student'), 'user', 
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class)
                    ->required()
                    ->placeholder(__('admin.select_student')),
                
                Flex::make([
                    Date::make(__('admin.start_date'), 'start_date')
                        ->required(),
                    
                    Date::make(__('admin.end_date'), 'end_date')
                        ->required(),
                ]),
                /*
                Flex::make([
                    Select::make(__('admin.status'), 'status')
                        ->options(ProgramTaskInstance::getStatuses())
                        ->required()
                        ->default('pending'),
                    
                    Select::make(__('admin.assigned_via'), 'assigned_via')
                        ->options(ProgramTaskInstance::getAssignmentTypes())
                        ->required()
                        ->default('individual'),
                ]),
                */
                BelongsTo::make(__('admin.team'), 'team', 
                    formatted: fn(ProgramTeam $team) => $team->name,
                    resource: ProgramTeamResource::class)
                    ->nullable()
                    ->placeholder(__('admin.select_team'))
                    ->showWhen('assigned_via', 'team'),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.task_name'), 'programTask.name'),
            Text::make(__('admin.program_name'), 'programTask.program.name'),
            Text::make(__('admin.task_type'), 'programTask.task_type_name')
                ->badge('blue'),
            BelongsTo::make(__('admin.student'), 'user', 
                formatted: fn(User $user) => $user->name),
            Date::make(__('admin.start_date'), 'start_date')
                ->format('d.m.Y'),
            Date::make(__('admin.end_date'), 'end_date')
                ->format('d.m.Y'),
            Number::make(__('admin.duration'), 'duration')
                ->badge('gray'),
                /*
            Text::make(__('admin.status'), 'status_name')
                ->badge(fn($item) => match($item->status) {
                    'pending' => 'blue',
                    'completed' => 'green',
                    'missed' => 'red',
                    'excused' => 'yellow',
                    default => 'gray'
                }),
                */
            Number::make(__('admin.days_remaining'), 'days_remaining')
                ->badge(fn($value) => $value <= 0 ? 'red' : ($value <= 3 ? 'yellow' : 'green')),
            Text::make(__('admin.assigned_via'), 'assignment_type_name')
                ->badge('purple'),
            BelongsTo::make(__('admin.team'), 'team', 
                formatted: fn(ProgramTeam $team) => $team->name),
            ...parent::getCommonDetailFields(),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.program_tasks'), 'programTask', 
                formatted: fn(ProgramTask $task) => $task->name,
                resource: ProgramTaskResource::class),
            BelongsTo::make(__('admin.student'), 'user', 
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class),
            Select::make(__('admin.status'), 'status')
                ->options(ProgramTaskInstance::getStatuses()),
            Select::make(__('admin.assigned_via'), 'assigned_via')
                ->options(ProgramTaskInstance::getAssignmentTypes()),
            BelongsTo::make(__('admin.team'), 'team', 
                formatted: fn(ProgramTeam $team) => $team->name,
                resource: ProgramTeamResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_task_id' => ['required', 'exists:program_tasks,id'],
            'user_id' => ['required', 'exists:users,id'],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after_or_equal:start_date'],
            'status' => ['required', 'in:pending,completed,missed,excused'],
            'assigned_via' => ['required', 'in:individual,team'],
            'team_id' => ['nullable', 'exists:program_teams,id'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['programTask.name', 'user.name'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }
}
