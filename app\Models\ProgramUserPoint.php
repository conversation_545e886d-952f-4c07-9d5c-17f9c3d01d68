<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProgramUserPoint extends BaseModel
{
    use SoftDeletes;

    /**
     * Point source constants.
     */
    const SOURCE_ACHIEVEMENT = 1;
    const SOURCE_TASK = 2;
    const SOURCE_QUEST = 3;
    const SOURCE_READING = 4;
    const SOURCE_BONUS = 5;
    const SOURCE_QUIZ = 6;
    const SOURCE_ACTIVITY = 7;

    /**
     * The table associated with the model.
     */
    protected $table = 'program_user_points';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_id',
        'term_user_id',
        'point_source',
        'points',
        'earned_at',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'point_source' => 'integer',
            'points' => 'integer',
            'earned_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get all point sources.
     */
    public static function getPointSources(): array
    {
        return [
            self::SOURCE_ACHIEVEMENT => 'Achievement',
            self::SOURCE_TASK => 'Task',
            self::SOURCE_QUEST => 'Quest',
            self::SOURCE_READING => 'Reading',
            self::SOURCE_BONUS => 'Bonus',
            self::SOURCE_QUIZ => 'Quiz',
            self::SOURCE_ACTIVITY => 'Activity',
        ];
    }

    /**
     * Get the program.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the term user (student).
     */
    public function termUser(): BelongsTo
    {
        return $this->belongsTo(TermUser::class);
    }

    /**
     * Get point source display name.
     */
    public function getPointSourceDisplayNameAttribute(): string
    {
        return self::getPointSources()[$this->point_source] ?? 'Unknown';
    }

    /**
     * Scope to get points by source.
     */
    public function scopeBySource($query, int $source)
    {
        return $query->where('point_source', $source);
    }

    /**
     * Scope to get recent points.
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('earned_at', '>=', now()->subDays($days));
    }

    /**
     * Scope to get points within date range.
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('earned_at', [$startDate, $endDate]);
    }
}
