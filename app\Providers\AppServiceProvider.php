<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register the middleware
//        $this->app['router']->aliasMiddleware('handle-api-requests', \App\Http\Middleware\HandleApiRequests::class);
//        $this->app['router']->aliasMiddleware('moonshine-auth', \App\Http\Middleware\MoonShineAuthentication::class);
//        $this->app['router']->aliasMiddleware('custom-csrf', \App\Http\Middleware\CustomVerifyCsrfToken::class);
    }
}


