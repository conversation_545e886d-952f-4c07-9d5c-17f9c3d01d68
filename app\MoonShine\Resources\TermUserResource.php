<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\TermUser;
use App\Models\User;
use App\Models\Role;
use App\Models\Organization;
use App\Models\SchoolClass;
use App\Models\Term;
use App\Helpers\ActiveTermHelper;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\ID;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Text;
use MoonShine\Support\Attributes\Icon;


#[Icon('user-group')]

class TermUserResource extends BaseResource
{
    protected string $model = TermUser::class;

    protected string $column = 'display_name';

    protected array $with = ['user', 'role', 'organization', 'schoolClass', 'term', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.term_users');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.user'), 'user', 
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class)
                ->sortable(),
            
            BelongsTo::make(__('admin.role'), 'role', 
                formatted: fn(Role $role) => $role->name,
                resource: RoleResource::class)
                ->sortable(),
            
            BelongsTo::make(__('admin.organization'), 'organization', 
                formatted: fn(Organization $org) => $org->name,
                resource: OrganizationResource::class)
                ->nullable()
                ->sortable(),
            
            BelongsTo::make(__('admin.class'), 'schoolClass', 
                formatted: fn(SchoolClass $class) => $class->full_name,
                resource: SchoolClassResource::class)
                ->nullable()
                ->sortable(),
            
            BelongsTo::make(__('admin.term'), 'term', 
                formatted: fn(Term $term) => $term->name,
                resource: TermResource::class)
                ->sortable(),
            
            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    BelongsTo::make(__('admin.user'), 'user', 
                        formatted: fn(User $user) => $user->name,
                        resource: UserResource::class)
                        ->required()
                        ->placeholder(__('admin.select_user')),
                    
                    BelongsTo::make(__('admin.role'), 'role', 
                        formatted: fn(Role $role) => $role->name,
                        resource: RoleResource::class)
                        ->required()
                        ->placeholder(__('admin.select_role'))
                ]),
                
                Flex::make([
                    BelongsTo::make(__('admin.organization'), 'organization', 
                        formatted: fn(Organization $org) => $org->name,
                        resource: OrganizationResource::class)
                        ->nullable()
                        ->placeholder(__('admin.select_organization'))
                        ->valuesQuery(function ($query) {
                            return $query->where('active', true);
                        }),
                    
                    BelongsTo::make(__('admin.class'), 'schoolClass', 
                        formatted: fn(SchoolClass $class) => $class->full_name,
                        resource: SchoolClassResource::class)
                        ->nullable()
                        ->placeholder(__('admin.select_class')),
                ]),
                
                Flex::make([
                    BelongsTo::make(__('admin.term'), 'term', 
                        formatted: fn(Term $term) => $term->name,
                        resource: TermResource::class)
                        ->required()
                        ->placeholder(__('admin.select_term'))
                        ->valuesQuery(function ($query) {
                            return $query->where('active', true);
                        }),
                    
                    Switcher::make(__('admin.active'), 'active')
                        ->default(true),
                ]),
            ]),
            
            
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
                BelongsTo::make(__('admin.user'), 'user', 
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class),
                
                BelongsTo::make(__('admin.role'), 'role', 
                    formatted: fn(Role $role) => $role->name, resource: RoleResource::class),
                
                BelongsTo::make(__('admin.organization'), 'organization', 
                    formatted: fn(Organization $org) => $org->name,
                    resource: OrganizationResource::class)
                    ->nullable(),
                
                BelongsTo::make(__('admin.class'), 'schoolClass', 
                    formatted: fn(SchoolClass $class) => $class->full_name,
                    resource: SchoolClassResource::class)
                    ->nullable(),
                
                BelongsTo::make(__('admin.term'), 'term', 
                    formatted: fn(Term $term) => $term->name,resource: TermResource::class),
                
                Switcher::make(__('admin.active'), 'active'),
                
                Text::make(__('admin.summary'), 'summary'),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'role_id' => ['required', 'exists:roles,id'],
            'organization_id' => ['nullable', 'exists:organizations,id'],
            'class_id' => ['nullable', 'exists:school_classes,id'],
            'term_id' => ['required', 'exists:terms,id'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['user.name', 'user.email'];
    }

    protected function getDefaultSort(): array
    {
        return ['created_at' => 'desc'];
    }
}
