<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class ProgramBookQuiz extends BaseModel
{
    use SoftDeletes;

    /**
     * Quiz type constants.
     */
    const TYPE_COMPLETION = 'completion';
    const TYPE_DAILY_READING = 'daily_reading';
    const TYPE_PRACTICE = 'practice';

    /**
     * The table associated with the model.
     */
    protected $table = 'program_book_quizzes';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_id',
        'book_id',
        'user_id',
        'quiz_type',
        'total_questions',
        'correct_answers',
        'score_percentage',
        'passing_score',
        'is_passed',
        'attempt_number',
        'started_at',
        'completed_at',
        'time_limit_minutes',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'is_passed' => 'boolean',
            'score_percentage' => 'decimal:2',
            'passing_score' => 'decimal:2',
            'total_questions' => 'integer',
            'correct_answers' => 'integer',
            'attempt_number' => 'integer',
            'time_limit_minutes' => 'integer',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get all quiz types.
     */
    public static function getQuizTypes(): array
    {
        return [
            self::TYPE_COMPLETION => 'Book Completion Quiz',
            self::TYPE_DAILY_READING => 'Daily Reading Quiz',
            self::TYPE_PRACTICE => 'Practice Quiz',
        ];
    }

    /**
     * Get quiz type name.
     */
    public function getQuizTypeNameAttribute(): string
    {
        return self::getQuizTypes()[$this->quiz_type] ?? 'Unknown';
    }

    /**
     * Get the program this quiz belongs to.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the book this quiz is about.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the user (student) taking the quiz.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the quiz questions.
     */
    public function quizQuestions(): HasMany
    {
        return $this->hasMany(ProgramBookQuizQuestion::class)->orderBy('question_order');
    }

    /**
     * Scope to get completed quizzes.
     */
    public function scopeCompleted($query)
    {
        return $query->whereNotNull('completed_at');
    }

    /**
     * Scope to get ongoing quizzes.
     */
    public function scopeOngoing($query)
    {
        return $query->whereNull('completed_at');
    }

    /**
     * Scope to get passed quizzes.
     */
    public function scopePassed($query)
    {
        return $query->where('is_passed', true);
    }

    /**
     * Scope to get failed quizzes.
     */
    public function scopeFailed($query)
    {
        return $query->where('is_passed', false)->whereNotNull('completed_at');
    }

    /**
     * Scope to filter by quiz type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('quiz_type', $type);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeForBook($query, int $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Check if quiz is completed.
     */
    public function getIsCompletedAttribute(): bool
    {
        return !is_null($this->completed_at);
    }

    /**
     * Check if quiz is ongoing.
     */
    public function getIsOngoingAttribute(): bool
    {
        return is_null($this->completed_at);
    }

    /**
     * Get quiz duration in minutes.
     */
    public function getDurationMinutesAttribute(): ?int
    {
        if ($this->started_at && $this->completed_at) {
            return $this->started_at->diffInMinutes($this->completed_at);
        }
        return null;
    }

    /**
     * Get time remaining in minutes.
     */
    public function getTimeRemainingMinutesAttribute(): ?int
    {
        if (!$this->time_limit_minutes || $this->is_completed) {
            return null;
        }
        
        $elapsed = $this->started_at->diffInMinutes(now());
        $remaining = $this->time_limit_minutes - $elapsed;
        
        return max(0, $remaining);
    }

    /**
     * Check if quiz is expired (time limit exceeded).
     */
    public function getIsExpiredAttribute(): bool
    {
        if (!$this->time_limit_minutes || $this->is_completed) {
            return false;
        }
        
        return $this->time_remaining_minutes <= 0;
    }

    /**
     * Calculate and update quiz score.
     */
    public function calculateScore(): void
    {
        $this->correct_answers = $this->quizQuestions()->where('is_correct', true)->count();
        
        if ($this->total_questions > 0) {
            $this->score_percentage = ($this->correct_answers / $this->total_questions) * 100;
            $this->is_passed = $this->score_percentage >= $this->passing_score;
        }
        
        $this->save();
    }

    /**
     * Complete the quiz.
     */
    public function complete(): void
    {
        if ($this->is_completed) {
            return;
        }
        
        $this->completed_at = now();
        $this->calculateScore();
        
        // Award points if passed
        if ($this->is_passed) {
            $this->awardPoints();
        }
    }

    /**
     * Award points for completing the quiz.
     */
    protected function awardPoints(): void
    {
        $basePoints = match($this->quiz_type) {
            self::TYPE_COMPLETION => 100,
            self::TYPE_DAILY_READING => 20,
            self::TYPE_PRACTICE => 10,
            default => 10,
        };
        
        // Bonus for high scores
        $scoreBonus = 0;
        if ($this->score_percentage >= 90) {
            $scoreBonus = $basePoints * 0.5; // 50% bonus for 90%+
        } elseif ($this->score_percentage >= 80) {
            $scoreBonus = $basePoints * 0.25; // 25% bonus for 80%+
        }
        
        $totalPoints = $basePoints + $scoreBonus;
        
        // Create point record
        ProgramUserPoint::create([
            'program_id' => $this->program_id,
            'term_user_id' => $this->user_id,
            'point_source' => ProgramUserPoint::SOURCE_QUIZ,
            'points' => (int) $totalPoints,
            'earned_at' => now(),
        ]);
    }

    /**
     * Get next attempt number for a user and book.
     */
    public static function getNextAttemptNumber(int $programId, int $bookId, int $userId, string $quizType): int
    {
        return static::where('program_id', $programId)
                    ->where('book_id', $bookId)
                    ->where('user_id', $userId)
                    ->where('quiz_type', $quizType)
                    ->max('attempt_number') + 1;
    }

    /**
     * Generate quiz questions.
     */
    public function generateQuestions(int $questionCount, ?string $difficulty = null, ?int $pageStart = null, ?int $pageEnd = null): void
    {
        // Get previous question IDs to avoid repetition
        $previousQuestionIds = static::where('book_id', $this->book_id)
                                   ->where('user_id', $this->user_id)
                                   ->where('quiz_type', $this->quiz_type)
                                   ->where('id', '!=', $this->id)
                                   ->with('quizQuestions')
                                   ->get()
                                   ->pluck('quizQuestions')
                                   ->flatten()
                                   ->pluck('book_question_id')
                                   ->toArray();
        
        // Generate questions
        $questions = BookQuestion::generateQuizQuestions(
            $this->book_id,
            $questionCount,
            $difficulty,
            $pageStart,
            $pageEnd,
            $previousQuestionIds
        );
        
        // Create quiz questions
        foreach ($questions as $index => $question) {
            $this->quizQuestions()->create([
                'book_question_id' => $question['id'],
                'question_order' => $index + 1,
            ]);
        }
        
        $this->total_questions = count($questions);
        $this->save();
    }

    /**
     * Get quiz progress.
     */
    public function getProgressAttribute(): array
    {
        $totalQuestions = $this->quizQuestions()->count();
        $answeredQuestions = $this->quizQuestions()->whereNotNull('student_answer')->count();
        
        return [
            'total_questions' => $totalQuestions,
            'answered_questions' => $answeredQuestions,
            'remaining_questions' => $totalQuestions - $answeredQuestions,
            'progress_percentage' => $totalQuestions > 0 ? 
                round(($answeredQuestions / $totalQuestions) * 100, 2) : 0,
        ];
    }

    /**
     * Get quiz results summary.
     */
    public function getResultsAttribute(): array
    {
        return [
            'score_percentage' => $this->score_percentage,
            'correct_answers' => $this->correct_answers,
            'total_questions' => $this->total_questions,
            'is_passed' => $this->is_passed,
            'passing_score' => $this->passing_score,
            'attempt_number' => $this->attempt_number,
            'duration_minutes' => $this->duration_minutes,
            'quiz_type_name' => $this->quiz_type_name,
        ];
    }
}
