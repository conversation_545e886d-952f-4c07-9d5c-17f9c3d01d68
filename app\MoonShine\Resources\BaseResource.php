<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use MoonShine\Laravel\Resources\ModelResource;
use MoonShine\Support\ListOf;
use MoonShine\UI\Components\ActionButton;
use MoonShine\UI\Components\Collapse;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;


use App\Models\User;

abstract class BaseResource extends ModelResource
{
    /**
     * Get common audit fields for forms.
     */
    protected function getAuditFields(): array
    {
        if(!$this->isDetailPage()) {
            return [];
        }
        else {
            return [
                Collapse::make(__('admin.audit_info'), [
                    Text::make(__('admin.created_by'), 'creator.name'),
                    Text::make(__('admin.updated_by'), 'updater.name'),
                    Date::make(__('admin.created_at'), 'created_at')
                        ->format('d.m.Y H:i'),
                    Date::make(__('admin.updated_at'), 'updated_at')
                        ->format('d.m.Y H:i'),
                ])->closed(),
            ];
        }
    }

    /**
     * Get common index fields.
     */
    protected function getCommonIndexFields(): array
    {
        return [
//            ID::make()->sortable(),
        ];
    }

    /**
     * Get common detail fields.
     */
    protected function getCommonDetailFields(): array
    {
        return [
  //          ID::make(),
            Date::make(__('admin.created_at'), 'created_at')
                ->format('d.m.Y H:i'),
            Date::make(__('admin.updated_at'), 'updated_at')
                ->format('d.m.Y H:i'),
            Text::make(__('admin.created_by'), 'creator.name'),
            Text::make(__('admin.updated_by'), 'updater.name'),
        ];
    }

    /**
     * Common validation rules.
     */
    protected function getCommonRules(mixed $item = null): array
    {
        return [
            'created_by' => 'nullable|exists:users,id',
            'updated_by' => 'nullable|exists:users,id',
        ];
    }

    /**
     * Rules method with proper signature.
     */
    public function rules(mixed $item): array
    {
        return $this->getCommonRules($item);
    }

    /**
     * Get translatable title.
     */
    public function getTitle(): string
    {
        return __('admin.' . strtolower(class_basename($this->getModel())));
    }

    /**
     * Get search fields for the resource.
     */
    protected function getSearchFields(): array
    {
        return ['name'];
    }

    /**
     * Apply search to query.
     */
    protected function applySearch($query, string $search)
    {
        $searchFields = $this->getSearchFields();
        
        $query->where(function ($q) use ($search, $searchFields) {
            foreach ($searchFields as $field) {
                $q->orWhere($field, 'like', '%' . $search . '%');
            }
        });
        
        return $query;
    }

    /**
     * Get items per page.
     */
    protected function getItemsPerPage(): int
    {
        return 25;
    }

    /**
     * Get default sort field.
     */
    protected function getDefaultSort(): array
    {
        return ['id' => 'desc'];
    }

    protected function formBuilderButtons(): ListOf
    {
        return parent::formBuilderButtons()
            ->add(
                ActionButton::make(__('admin.cancel'), fn() => $this->getIndexPageUrl())->secondary()->class('btn-lg')
            );
    }

    /**
     * Redirect to index page after save.
     */
    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

}
