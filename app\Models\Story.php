<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

class Story extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'description',
        'cover_image',
        'map_grid_rows',
        'map_grid_columns',
        'map_background_image',
        'active',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'active' => 'boolean',
            'map_grid_rows' => 'integer',
            'map_grid_columns' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get the chapters for this story.
     */
    public function chapters(): HasMany
    {
        return $this->hasMany(StoryChapter::class)->orderBy('sequence');
    }

    /**
     * Get the characters for this story.
     */
    public function characters(): HasMany
    {
        return $this->hasMany(StoryCharacter::class);
    }

    /**
     * Get the active characters for this story.
     */
    public function activeCharacters(): Has<PERSON><PERSON>
    {
        return $this->hasMany(StoryCharacter::class)->where('active', true);
    }

    /**
     * Get the achievements for this story.
     */
    public function achievements(): Has<PERSON>any
    {
        return $this->hasMany(StoryAchievement::class);
    }

    /**
     * Get the story books (books associated with this story).
     */
    public function storyBooks(): HasMany
    {
        return $this->hasMany(StoryBook::class);
    }

    /**
     * Get the books associated with this story through story_books.
     */
    public function books(): BelongsToMany
    {
        return $this->belongsToMany(Book::class, 'story_books')
                    ->withPivot(['sequence', 'created_by', 'updated_by', 'deleted_by', 'deleted_at'])
                    ->withTimestamps()
                    ->orderBy('sequence');
    }

    /**
     * Get the rules for this story.
     */
    public function rules(): HasMany
    {
        return $this->hasMany(StoryRule::class);
    }

    /**
     * Scope to get active stories.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Get the total number of chapters.
     */
    public function getChapterCountAttribute(): int
    {
        return $this->chapters()->count();
    }

    /**
     * Get the total number of characters.
     */
    public function getCharacterCountAttribute(): int
    {
        return $this->characters()->count();
    }

    /**
     * Get the total number of achievements.
     */
    public function getAchievementCountAttribute(): int
    {
        return $this->achievements()->count();
    }

    /**
     * Check if story has chapters.
     */
    public function hasChapters(): bool
    {
        return $this->chapter_count > 0;
    }

    /**
     * Check if story has characters.
     */
    public function hasCharacters(): bool
    {
        return $this->character_count > 0;
    }

    /**
     * Check if story has achievements.
     */
    public function hasAchievements(): bool
    {
        return $this->achievement_count > 0;
    }

    /**
     * Get map dimensions as string.
     */
    public function getMapDimensionsAttribute(): string
    {
        return $this->map_grid_rows . 'x' . $this->map_grid_columns;
    }

    /**
     * Scope to search stories by title.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('title', 'like', '%' . $search . '%')
                    ->orWhere('description', 'like', '%' . $search . '%');
    }
}
