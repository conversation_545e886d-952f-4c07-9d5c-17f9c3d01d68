<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\{Story, StoryRule, StoryRuleDetail, StoryChapter, StoryCharacter, StoryCharacterStage, StoryAchievement, User, Role};

class GamificationModelsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user for audit fields
        $role = Role::create([
            'name' => 'Test Admin',
            'description' => 'Test Role',
            'level' => 1,
        ]);
        
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'role_id' => $role->id,
        ]);
        
        $this->actingAs($this->user);
    }

    public function test_story_creation_and_relationships()
    {
        // Create a story
        $story = Story::create([
            'title' => 'Test Adventure',
            'description' => 'A test adventure story',
            'cover_image' => 'test-cover.jpg',
            'map_grid_rows' => 10,
            'map_grid_columns' => 10,
            'map_background_image' => 'test-bg.jpg',
            'active' => true,
        ]);

        $this->assertDatabaseHas('stories', [
            'title' => 'Test Adventure',
            'active' => true,
        ]);

        // Test story attributes
        $this->assertEquals('10x10', $story->map_dimensions);
        $this->assertTrue($story->active);
        $this->assertEquals(0, $story->chapter_count);
        $this->assertEquals(0, $story->character_count);
        $this->assertEquals(0, $story->achievement_count);
    }

    public function test_story_rule_creation()
    {
        $story = Story::create([
            'title' => 'Test Story',
            'description' => 'Test description',
            'cover_image' => 'test.jpg',
            'map_grid_rows' => 5,
            'map_grid_columns' => 5,
            'active' => true,
        ]);

        $rule = StoryRule::create([
            'story_id' => $story->id,
            'rule_type' => StoryRule::TYPE_POINTS,
            'quantity' => 100,
        ]);

        $this->assertDatabaseHas('story_rules', [
            'story_id' => $story->id,
            'rule_type' => StoryRule::TYPE_POINTS,
            'quantity' => 100,
        ]);

        $this->assertEquals('Points', $rule->rule_type_name);
        $this->assertEquals('Points: 100', $rule->description);
        $this->assertTrue($rule->requiresQuantity());
        $this->assertFalse($rule->requiresSpecificItems());
    }

    public function test_story_chapter_creation()
    {
        $story = Story::create([
            'title' => 'Test Story',
            'description' => 'Test description',
            'cover_image' => 'test.jpg',
            'map_grid_rows' => 5,
            'map_grid_columns' => 5,
            'active' => true,
        ]);

        $chapter = StoryChapter::create([
            'story_id' => $story->id,
            'title' => 'Chapter One',
            'description' => 'The beginning',
            'sequence' => 1,
            'map_start_x' => 0,
            'map_start_y' => 0,
            'map_end_x' => 2,
            'map_end_y' => 2,
        ]);

        $this->assertDatabaseHas('story_chapters', [
            'story_id' => $story->id,
            'title' => 'Chapter One',
            'sequence' => 1,
        ]);

        $this->assertEquals('Chapter 1: Chapter One', $chapter->full_title);
        $this->assertTrue($chapter->hasMapCoordinates());
        $this->assertTrue($chapter->isFirst());
        $this->assertTrue($chapter->isLast());
        $this->assertEquals(['x' => 0, 'y' => 0], $chapter->start_coordinates);
        $this->assertEquals(['x' => 2, 'y' => 2], $chapter->end_coordinates);
    }

    public function test_story_character_and_stages()
    {
        $story = Story::create([
            'title' => 'Test Story',
            'description' => 'Test description',
            'cover_image' => 'test.jpg',
            'map_grid_rows' => 5,
            'map_grid_columns' => 5,
            'active' => true,
        ]);

        $character = StoryCharacter::create([
            'story_id' => $story->id,
            'name' => 'Hero',
            'description' => 'The main character',
            'base_image' => 'hero-base.jpg',
            'active' => true,
        ]);

        $stage1 = StoryCharacterStage::create([
            'character_id' => $character->id,
            'stage_number' => 1,
            'name' => 'Novice',
            'image' => 'hero-novice.jpg',
            'abilities' => ['basic_attack'],
        ]);

        $stage2 = StoryCharacterStage::create([
            'character_id' => $character->id,
            'stage_number' => 2,
            'name' => 'Warrior',
            'image' => 'hero-warrior.jpg',
            'abilities' => ['basic_attack', 'power_strike'],
        ]);

        $this->assertEquals(2, $character->stage_count);
        $this->assertEquals(2, $character->max_stage);
        $this->assertTrue($character->hasStages());
        $this->assertTrue($character->canBeSelected());
        $this->assertEquals('2 stages available', $character->progression_summary);

        $this->assertEquals('Stage 1: Novice', $stage1->full_name);
        $this->assertTrue($stage1->isFirst());
        $this->assertFalse($stage1->isFinal());
        $this->assertEquals('basic_attack', $stage1->abilities_text);

        $this->assertEquals('Stage 2: Warrior', $stage2->full_name);
        $this->assertFalse($stage2->isFirst());
        $this->assertTrue($stage2->isFinal());
        $this->assertEquals('basic_attack, power_strike', $stage2->abilities_text);
    }

    public function test_story_achievement_creation()
    {
        $story = Story::create([
            'title' => 'Test Story',
            'description' => 'Test description',
            'cover_image' => 'test.jpg',
            'map_grid_rows' => 5,
            'map_grid_columns' => 5,
            'active' => true,
        ]);

        $achievement = StoryAchievement::create([
            'story_id' => $story->id,
            'name' => 'First Steps',
            'description' => 'Complete the first chapter',
            'type' => StoryAchievement::TYPE_BADGE,
            'image' => 'first-steps-badge.jpg',
            'map_start_x' => 1,
            'map_start_y' => 1,
            'is_dynamic_position' => false,
        ]);

        $this->assertDatabaseHas('story_achievements', [
            'story_id' => $story->id,
            'name' => 'First Steps',
            'type' => StoryAchievement::TYPE_BADGE,
        ]);

        $this->assertEquals('Badge', $achievement->type_display_name);
        $this->assertTrue($achievement->hasMapCoordinates());
        $this->assertFalse($achievement->hasDynamicPosition());
        $this->assertEquals(['x' => 1, 'y' => 1], $achievement->start_coordinates);
        $this->assertStringContainsString('Badge (Map: 1,1)', $achievement->info);
    }
}
