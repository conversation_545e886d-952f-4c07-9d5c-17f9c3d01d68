<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;
use App\Models\Term;
use App\Models\TermUser;
use App\Models\Organization;
use App\Models\SchoolClass;
use App\Models\GradeLevel;

class AssignTeacherToClass extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'assign:teacher-class';

    /**
     * The console command description.
     */
    protected $description = 'Assign a teacher to a class for testing role-based menu';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Assigning Teacher to Class ===');
        
        // Get or create active term
        $activeTerm = Term::where('active', true)->first();
        if (!$activeTerm) {
            $activeTerm = Term::create([
                'name' => 'Test Term ' . date('Y'),
                'start_date' => now()->subMonth(),
                'end_date' => now()->addMonth(),
                'active' => true,
            ]);
            $this->info("Created active term: {$activeTerm->name}");
        } else {
            $this->info("Using existing active term: {$activeTerm->name}");
        }

        // Get or create organization
        $organization = Organization::first();
        if (!$organization) {
            $organization = Organization::create([
                'name' => 'Test School',
                'type' => 'school',
                'active' => true,
            ]);
            $this->info("Created organization: {$organization->name}");
        } else {
            $this->info("Using existing organization: {$organization->name}");
        }

        // Get or create grade level
        $gradeLevel = GradeLevel::first();
        if (!$gradeLevel) {
            $gradeLevel = GradeLevel::create([
                'name' => 'Grade 1',
                'level' => 1,
            ]);
            $this->info("Created grade level: {$gradeLevel->name}");
        } else {
            $this->info("Using existing grade level: {$gradeLevel->name}");
        }

        // Get or create school class
        $schoolClass = SchoolClass::first();
        if (!$schoolClass) {
            $schoolClass = SchoolClass::create([
                'name' => 'Test Class A',
                'organization_id' => $organization->id,
                'grade_level_id' => $gradeLevel->id,
            ]);
            $this->info("Created school class: {$schoolClass->name}");
        } else {
            $this->info("Using existing school class: {$schoolClass->full_name}");
        }

        // Get teacher and student roles
        $teacherRole = Role::where('level', Role::LEVEL_TEACHER)->first();
        $studentRole = Role::where('level', Role::LEVEL_STUDENT)->first();

        if (!$teacherRole || !$studentRole) {
            $this->error('Teacher or Student role not found!');
            return 1;
        }

        // Get a teacher
        $teacher = User::where('role_id', $teacherRole->id)->first();
        if (!$teacher) {
            $this->error('No teacher found!');
            return 1;
        }

        // Check if teacher is already assigned to this class
        $existingAssignment = TermUser::where('user_id', $teacher->id)
            ->where('term_id', $activeTerm->id)
            ->where('class_id', $schoolClass->id)
            ->where('role_id', $teacherRole->id)
            ->first();

        if ($existingAssignment) {
            $this->info("Teacher {$teacher->name} is already assigned to {$schoolClass->full_name}");
        } else {
            // Assign teacher to class
            TermUser::create([
                'user_id' => $teacher->id,
                'role_id' => $teacherRole->id,
                'organization_id' => $organization->id,
                'class_id' => $schoolClass->id,
                'term_id' => $activeTerm->id,
                'active' => true,
            ]);
            $this->info("Assigned teacher {$teacher->name} to {$schoolClass->full_name}");
        }

        // Get some students and assign them to the same class
        $students = User::where('role_id', $studentRole->id)->limit(3)->get();
        
        foreach ($students as $student) {
            $existingStudentAssignment = TermUser::where('user_id', $student->id)
                ->where('term_id', $activeTerm->id)
                ->where('class_id', $schoolClass->id)
                ->where('role_id', $studentRole->id)
                ->first();

            if ($existingStudentAssignment) {
                $this->info("Student {$student->name} is already assigned to {$schoolClass->full_name}");
            } else {
                TermUser::create([
                    'user_id' => $student->id,
                    'role_id' => $studentRole->id,
                    'organization_id' => $organization->id,
                    'class_id' => $schoolClass->id,
                    'term_id' => $activeTerm->id,
                    'active' => true,
                ]);
                $this->info("Assigned student {$student->name} to {$schoolClass->full_name}");
            }
        }

        $this->newLine();
        $this->info('=== Assignment Complete ===');
        $this->info('You can now test the TeacherStudentsResource filtering.');
        
        return 0;
    }
}
