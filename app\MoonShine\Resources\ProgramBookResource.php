<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Book;
use App\Models\Program;
use App\Models\ProgramBook;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\BookResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;

/**
 * @extends BaseResource<ProgramBook>
 */
class ProgramBookResource extends BaseResource
{
    protected string $model = ProgramBook::class;

    protected array $with = ['program', 'book', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_books');
    }



    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name)
                ->sortable(),
            Text::make(__('admin.isbn'), 'book.isbn'),
            Text::make(__('admin.publishers'), 'book.publisher.name'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.programs'), 'program', 
                    formatted: fn(Program $program) => $program->name,
                    resource: ProgramResource::class)
                    ->required()
                    ->placeholder(__('admin.select_program')),
                
                BelongsTo::make(__('admin.books'), 'book', 
                    formatted: fn(Book $book) => $book->name . ' (' . $book->isbn . ')',
                    resource: BookResource::class)
                    ->required()
                    ->placeholder(__('admin.select_book'))
                    ->asyncSearch('name'),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name),
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name),
            Text::make(__('admin.isbn'), 'book.isbn'),
            Text::make(__('admin.publishers'), 'book.publisher.name'),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name,
                resource: BookResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'book_id' => ['required', 'exists:books,id'],
            ...parent::getCommonRules($item),
        ];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

}
