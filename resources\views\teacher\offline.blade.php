@extends('layouts.teacher')

@section('title', 'Offline - ' . __('teacher.app_title'))

@section('content')
<div class="teacher-container">
    <!-- Offline Status Card -->
    <x-moonshine::card class="mb-6 text-center">
        <div class="py-8">
            <div class="mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728"></path>
                </svg>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">You're Offline</h1>
            <p class="text-gray-600">
                Please check your internet connection and try again.
            </p>
        </div>
    </x-moonshine::card>

    <!-- Retry Actions Card -->
    <x-moonshine::card class="mb-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">What you can do:</h2>
        <div class="space-y-3">
            <x-moonshine::link-button 
                href="javascript:void(0)" 
                onclick="window.location.reload()" 
                class="btn-touch bg-purple-50 text-purple-700 border border-purple-200 hover:bg-purple-100 w-full flex items-center justify-center"
            >
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Try Again
            </x-moonshine::link-button>

            <x-moonshine::link-button 
                href="{{ route('teacher.dashboard') }}" 
                class="btn-touch bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100 w-full flex items-center justify-center"
            >
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                </svg>
                Go to Dashboard
            </x-moonshine::link-button>
        </div>
    </x-moonshine::card>

    <!-- Offline Tips Card -->
    <x-moonshine::card class="bg-blue-50 border border-blue-200">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800 mb-2">
                    Troubleshooting Tips
                </h3>
                <div class="text-sm text-blue-700 space-y-1">
                    <p>• Check your WiFi or mobile data connection</p>
                    <p>• Try moving to an area with better signal</p>
                    <p>• Restart your browser or app</p>
                    <p>• Contact your network administrator if the problem persists</p>
                </div>
            </div>
        </div>
    </x-moonshine::card>
</div>

@push('scripts')
<script>
    // Auto-retry connection every 30 seconds
    let retryInterval = setInterval(function() {
        if (navigator.onLine) {
            clearInterval(retryInterval);
            showToast('Connection restored! Redirecting...', 'success');
            setTimeout(() => {
                window.location.href = '{{ route('teacher.dashboard') }}';
            }, 1500);
        }
    }, 30000);

    // Listen for online event
    window.addEventListener('online', function() {
        clearInterval(retryInterval);
        showToast('Connection restored! Redirecting...', 'success');
        setTimeout(() => {
            window.location.href = '{{ route('teacher.dashboard') }}';
        }, 1500);
    });

    // Show connection status
    if (navigator.onLine) {
        showToast('You appear to be online. Try refreshing the page.', 'info');
    }
</script>
@endpush
@endsection
