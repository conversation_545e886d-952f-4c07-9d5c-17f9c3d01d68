<?php

namespace App\Services;

use App\Models\ProgramReadingLog;
use App\Models\ProgramUserBook;
use App\Models\ProgramTaskInstance;
use App\Models\ProgramTask;
use App\Models\ProgramBookQuiz;
use App\Services\AssessmentService;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ReadingLogService
{
    /**
     * Create a new reading log entry.
     */
    public function createReadingLog(
        int $programId,
        int $bookId,
        int $userId,
        Carbon $readingDate,
        int $startPage,
        int $endPage,
        ?int $durationMinutes = null,
        ?string $notes = null,
        ?int $taskInstanceId = null
    ): ProgramReadingLog {
        // Validate that entry can be created
        $validation = ProgramReadingLog::canCreateEntry($programId, $bookId, $userId, $readingDate);
        
        if (!$validation['can_create']) {
            throw new \Exception($validation['reason']);
        }
        
        // Validate page range
        if ($endPage < $startPage) {
            throw new \Exception('End page must be greater than or equal to start page');
        }
        
        $readingLog = ProgramReadingLog::create([
            'program_id' => $programId,
            'book_id' => $bookId,
            'user_id' => $userId,
            'program_task_instance_id' => $taskInstanceId,
            'reading_date' => $readingDate,
            'start_page' => $startPage,
            'end_page' => $endPage,
            'reading_duration_minutes' => $durationMinutes,
            'reading_notes' => $notes,
        ]);
        
        // Update book progress
        $this->updateBookProgress($programId, $bookId, $userId);
        
        // Check if this triggers a daily reading quiz
        $this->checkDailyReadingQuizTrigger($readingLog);
        
        return $readingLog;
    }

    /**
     * Verify a reading log entry.
     */
    public function verifyReadingLog(ProgramReadingLog $readingLog, int $verifierId, ?string $notes = null): bool
    {
        return $readingLog->verify($verifierId, $notes);
    }

    /**
     * Bulk verify multiple reading logs.
     */
    public function bulkVerifyReadingLogs(array $logIds, int $verifierId, ?string $notes = null): array
    {
        $results = ['verified' => 0, 'skipped' => 0, 'errors' => []];
        
        foreach ($logIds as $logId) {
            try {
                $log = ProgramReadingLog::find($logId);
                
                if (!$log) {
                    $results['errors'][] = "Log ID {$logId} not found";
                    continue;
                }
                
                if ($log->is_verified) {
                    $results['skipped']++;
                    continue;
                }
                
                if ($this->verifyReadingLog($log, $verifierId, $notes)) {
                    $results['verified']++;
                } else {
                    $results['errors'][] = "Failed to verify log ID {$logId}";
                }
            } catch (\Exception $e) {
                $results['errors'][] = "Error verifying log ID {$logId}: " . $e->getMessage();
            }
        }
        
        return $results;
    }

    /**
     * Update book reading progress based on reading logs.
     */
    public function updateBookProgress(int $programId, int $bookId, int $userId): void
    {
        // Find term_user_id from user_id
        $termUser = \App\Models\TermUser::where('user_id', $userId)->first();

        if (!$termUser) {
            return;
        }

        $assignment = ProgramUserBook::where('program_id', $programId)
                                    ->where('book_id', $bookId)
                                    ->where('term_user_id', $termUser->id)
                                    ->first();
        
        if (!$assignment) {
            return;
        }
        
        $logs = ProgramReadingLog::where('program_id', $programId)
                                ->where('book_id', $bookId)
                                ->where('user_id', $userId)
                                ->get();
        
        $highestPageRead = $logs->max('end_page') ?? 0;
        
        // If book has total pages, calculate completion percentage
        if ($assignment->book && $assignment->book->total_pages > 0) {
            $completionPercentage = ($highestPageRead / $assignment->book->total_pages) * 100;
            
            // Mark as completed if 100% read
            if ($completionPercentage >= 100 && !$assignment->is_completed) {
                $assignment->markCompleted();
            }
        }
    }

    /**
     * Check if reading log should trigger a daily reading quiz.
     */
    protected function checkDailyReadingQuizTrigger(ProgramReadingLog $readingLog): void
    {
        // Only trigger for substantial reading sessions (5+ pages)
        if ($readingLog->pages_read < 5) {
            return;
        }
        
        // Check if daily reading quiz already exists for this date
        $existingQuiz = ProgramBookQuiz::where('program_id', $readingLog->program_id)
                                     ->where('book_id', $readingLog->book_id)
                                     ->where('user_id', $readingLog->user_id)
                                     ->where('quiz_type', ProgramBookQuiz::TYPE_DAILY_READING)
                                     ->whereDate('started_at', $readingLog->reading_date)
                                     ->first();
        
        if ($existingQuiz) {
            return; // Quiz already exists
        }
        
        // Generate daily reading quiz
        $assessmentService = new AssessmentService();
        
        try {
            $assessmentService->generateDailyReadingQuiz(
                $readingLog->program_id,
                $readingLog->book_id,
                $readingLog->user_id,
                $readingLog->start_page,
                $readingLog->end_page,
                3, // 3 questions
                70.0 // 70% passing score
            );
        } catch (\Exception $e) {
            // Silently fail if quiz generation fails (e.g., not enough questions)
            \Log::warning("Failed to generate daily reading quiz: " . $e->getMessage());
        }
    }

    /**
     * Get reading statistics for a student.
     */
    public function getStudentReadingStatistics(int $programId, int $userId, ?int $bookId = null): array
    {
        return ProgramReadingLog::getStudentStatistics($programId, $userId, $bookId);
    }

    /**
     * Get reading logs for a date range.
     */
    public function getReadingLogsForDateRange(
        int $programId, 
        Carbon $startDate, 
        Carbon $endDate, 
        ?int $userId = null
    ): array {
        return ProgramReadingLog::getLogsForDateRange($programId, $startDate, $endDate, $userId);
    }

    /**
     * Get reading streak information for a student.
     */
    public function getReadingStreak(int $programId, int $bookId, int $userId): array
    {
        $logs = ProgramReadingLog::where('program_id', $programId)
                                ->where('book_id', $bookId)
                                ->where('user_id', $userId)
                                ->orderBy('reading_date', 'desc')
                                ->get();
        
        if ($logs->isEmpty()) {
            return [
                'current_streak' => 0,
                'longest_streak' => 0,
                'last_reading_date' => null,
                'streak_dates' => [],
            ];
        }
        
        $currentStreak = $logs->first()->calculateReadingStreak();
        $longestStreak = $this->calculateLongestStreak($logs);
        $streakDates = $this->getStreakDates($logs, $currentStreak);
        
        return [
            'current_streak' => $currentStreak,
            'longest_streak' => $longestStreak,
            'last_reading_date' => $logs->first()->reading_date,
            'streak_dates' => $streakDates,
        ];
    }

    /**
     * Calculate the longest reading streak.
     */
    protected function calculateLongestStreak(Collection $logs): int
    {
        if ($logs->isEmpty()) {
            return 0;
        }
        
        $dates = $logs->pluck('reading_date')->map(fn($date) => $date->toDateString())->unique()->sort();
        $longestStreak = 0;
        $currentStreak = 1;
        
        for ($i = 1; $i < $dates->count(); $i++) {
            $currentDate = Carbon::parse($dates[$i]);
            $previousDate = Carbon::parse($dates[$i - 1]);
            
            if ($currentDate->diffInDays($previousDate) === 1) {
                $currentStreak++;
            } else {
                $longestStreak = max($longestStreak, $currentStreak);
                $currentStreak = 1;
            }
        }
        
        return max($longestStreak, $currentStreak);
    }

    /**
     * Get dates that make up the current streak.
     */
    protected function getStreakDates(Collection $logs, int $streakLength): array
    {
        if ($streakLength === 0) {
            return [];
        }
        
        $dates = [];
        $currentDate = now();
        
        for ($i = 0; $i < $streakLength; $i++) {
            $checkDate = $currentDate->subDays($i)->toDateString();
            $hasLog = $logs->where('reading_date', $checkDate)->isNotEmpty();
            
            if ($hasLog) {
                $dates[] = $checkDate;
            } else {
                break;
            }
        }
        
        return array_reverse($dates);
    }

    /**
     * Generate reading log tasks for a program.
     */
    public function generateReadingLogTasks(
        int $programId,
        array $userIds,
        Carbon $startDate,
        Carbon $endDate,
        ?int $bookId = null,
        int $pointsPerLog = 10
    ): array {
        $created = 0;
        $errors = [];
        
        // Create the reading log task
        $task = ProgramTask::create([
            'program_id' => $programId,
            'name' => 'Daily Reading Log',
            'description' => 'Complete daily reading log entries',
            'task_type' => ProgramTask::TYPE_READING_LOG,
            'is_recurring' => true,
            'recurrence_pattern' => ProgramTask::RECURRENCE_DAILY,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'points' => $pointsPerLog,
            'book_id' => $bookId,
        ]);
        
        // Create task instances for each user and date
        $currentDate = $startDate->copy();
        
        while ($currentDate->lte($endDate)) {
            foreach ($userIds as $userId) {
                try {
                    ProgramTaskInstance::create([
                        'program_task_id' => $task->id,
                        'user_id' => $userId,
                        'start_date' => $currentDate->toDateString(),
                        'end_date' => $currentDate->toDateString(),
                        'status' => ProgramTaskInstance::STATUS_PENDING,
                        'assigned_via' => ProgramTaskInstance::ASSIGNED_INDIVIDUAL,
                    ]);
                    
                    $created++;
                } catch (\Exception $e) {
                    $errors[] = "Failed to create task for user {$userId} on {$currentDate->toDateString()}: " . $e->getMessage();
                }
            }
            
            $currentDate->addDay();
        }
        
        return [
            'task_id' => $task->id,
            'instances_created' => $created,
            'errors' => $errors,
        ];
    }

    /**
     * Get reading log dashboard data for a program.
     */
    public function getReadingLogDashboard(int $programId, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $startDate = $startDate ?? now()->subDays(30);
        $endDate = $endDate ?? now();
        
        $logs = ProgramReadingLog::where('program_id', $programId)
                                ->betweenDates($startDate, $endDate)
                                ->with(['user', 'book'])
                                ->get();
        
        return [
            'total_logs' => $logs->count(),
            'total_pages_read' => $logs->sum('pages_read'),
            'total_points_awarded' => $logs->sum('points_awarded'),
            'verified_logs' => $logs->where('is_verified', true)->count(),
            'unverified_logs' => $logs->where('is_verified', false)->count(),
            'unique_students' => $logs->pluck('user_id')->unique()->count(),
            'unique_books' => $logs->pluck('book_id')->unique()->count(),
            'average_pages_per_session' => $logs->count() > 0 ? round($logs->avg('pages_read'), 1) : 0,
            'average_duration_minutes' => $logs->where('reading_duration_minutes', '>', 0)->avg('reading_duration_minutes') ?? 0,
            'daily_breakdown' => $this->getDailyBreakdown($logs),
            'top_readers' => $this->getTopReaders($logs),
            'book_popularity' => $this->getBookPopularity($logs),
        ];
    }

    /**
     * Get daily breakdown of reading activity.
     */
    protected function getDailyBreakdown(Collection $logs): array
    {
        return $logs->groupBy(fn($log) => $log->reading_date->toDateString())
                   ->map(function($dayLogs, $date) {
                       return [
                           'date' => $date,
                           'total_logs' => $dayLogs->count(),
                           'total_pages' => $dayLogs->sum('pages_read'),
                           'unique_students' => $dayLogs->pluck('user_id')->unique()->count(),
                       ];
                   })
                   ->sortBy('date')
                   ->values()
                   ->toArray();
    }

    /**
     * Get top readers by pages read.
     */
    protected function getTopReaders(Collection $logs): array
    {
        return $logs->groupBy('user_id')
                   ->map(function($userLogs, $userId) {
                       $user = $userLogs->first()->user;
                       return [
                           'user_id' => $userId,
                           'user_name' => $user->name,
                           'total_pages' => $userLogs->sum('pages_read'),
                           'total_sessions' => $userLogs->count(),
                           'total_points' => $userLogs->sum('points_awarded'),
                           'average_pages_per_session' => round($userLogs->avg('pages_read'), 1),
                       ];
                   })
                   ->sortByDesc('total_pages')
                   ->take(10)
                   ->values()
                   ->toArray();
    }

    /**
     * Get book popularity by reading activity.
     */
    protected function getBookPopularity(Collection $logs): array
    {
        return $logs->groupBy('book_id')
                   ->map(function($bookLogs, $bookId) {
                       $book = $bookLogs->first()->book;
                       return [
                           'book_id' => $bookId,
                           'book_name' => $book->name,
                           'total_sessions' => $bookLogs->count(),
                           'total_pages_read' => $bookLogs->sum('pages_read'),
                           'unique_readers' => $bookLogs->pluck('user_id')->unique()->count(),
                           'average_pages_per_session' => round($bookLogs->avg('pages_read'), 1),
                       ];
                   })
                   ->sortByDesc('total_sessions')
                   ->values()
                   ->toArray();
    }
}
