const CACHE_NAME = 'student-reading-v1';
const OFFLINE_URL = '/student/offline';

// Files to cache for offline functionality
const urlsToCache = [
  '/student',
  '/student/dashboard',
  '/student/login',
  '/student/splash',
  '/student/offline',
  '/vendor/moonshine/assets/main.css',
  '/build/assets/app.css',
  '/build/assets/app.js',
  // Add more static assets as needed
];

// Install event - cache resources
self.addEventListener('install', event => {
  console.log('Student Service Worker installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Caching student app resources');
        return cache.addAll(urlsToCache);
      })
      .then(() => {
        // Force the waiting service worker to become the active service worker
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Student Service Worker activating...');
  
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      // Ensure the new service worker takes control immediately
      return self.clients.claim();
    })
  );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
  // Only handle GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) {
    return;
  }

  // Handle student routes specifically
  if (event.request.url.includes('/student')) {
    event.respondWith(
      caches.match(event.request)
        .then(response => {
          // Return cached version if available
          if (response) {
            return response;
          }

          // Try to fetch from network
          return fetch(event.request)
            .then(response => {
              // Don't cache non-successful responses
              if (!response || response.status !== 200 || response.type !== 'basic') {
                return response;
              }

              // Clone the response for caching
              const responseToCache = response.clone();

              caches.open(CACHE_NAME)
                .then(cache => {
                  cache.put(event.request, responseToCache);
                });

              return response;
            })
            .catch(() => {
              // If network fails, show offline page for navigation requests
              if (event.request.mode === 'navigate') {
                return caches.match(OFFLINE_URL);
              }
              
              // For other requests, try to return a cached version
              return caches.match(event.request);
            });
        })
    );
  } else {
    // For non-student routes, use network-first strategy
    event.respondWith(
      fetch(event.request)
        .then(response => {
          return response;
        })
        .catch(() => {
          return caches.match(event.request);
        })
    );
  }
});

// Background sync for when connection is restored
self.addEventListener('sync', event => {
  if (event.tag === 'student-background-sync') {
    console.log('Background sync triggered for student app');
    event.waitUntil(
      // Perform background sync tasks
      syncStudentData()
    );
  }
});

// Push notification handling
self.addEventListener('push', event => {
  console.log('Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'New reading challenge available!',
    icon: '/icons/student-icon-192x192.png',
    badge: '/icons/student-badge-72x72.png',
    vibrate: [200, 100, 200],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Start Reading',
        icon: '/icons/action-read.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/icons/action-close.png'
      }
    ],
    requireInteraction: true,
    tag: 'student-notification'
  };

  event.waitUntil(
    self.registration.showNotification('Reading Adventure', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
  console.log('Notification clicked');
  
  event.notification.close();

  if (event.action === 'explore') {
    // Open the student dashboard
    event.waitUntil(
      clients.openWindow('/student/dashboard')
    );
  } else if (event.action === 'close') {
    // Just close the notification
    return;
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/student')
    );
  }
});

// Message handling from the main thread
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// Helper function for background sync
async function syncStudentData() {
  try {
    // Sync reading progress, achievements, etc.
    console.log('Syncing student data...');
    
    // This would typically involve:
    // 1. Getting cached offline data
    // 2. Sending it to the server
    // 3. Updating local cache with server response
    
    return Promise.resolve();
  } catch (error) {
    console.error('Background sync failed:', error);
    throw error;
  }
}

// Periodic background sync (if supported)
self.addEventListener('periodicsync', event => {
  if (event.tag === 'student-periodic-sync') {
    event.waitUntil(
      syncStudentData()
    );
  }
});

// Handle app updates
self.addEventListener('appinstalled', event => {
  console.log('Student app installed successfully');
});

// Cache management utilities
function cleanupCache() {
  return caches.open(CACHE_NAME)
    .then(cache => {
      return cache.keys().then(requests => {
        // Remove old entries if cache gets too large
        if (requests.length > 50) {
          const oldRequests = requests.slice(0, requests.length - 40);
          return Promise.all(
            oldRequests.map(request => cache.delete(request))
          );
        }
      });
    });
}

// Run cache cleanup periodically
setInterval(cleanupCache, 24 * 60 * 60 * 1000); // Once per day
