<?php

namespace App\Helpers;

use App\Models\Term;
use Illuminate\Support\Facades\Session;

class ActiveTermHelper
{
    /**
     * Get the active term ID from session.
     */
    public static function getActiveTermId(): ?int
    {
        return Session::get('active_term_id');
    }

    /**
     * Get the active term name from session.
     */
    public static function getActiveTermName(): ?string
    {
        return Session::get('active_term_name');
    }

    /**
     * Get the active term model from session.
     * Falls back to database query if not in session.
     */
    public static function getActiveTerm(): ?Term
    {
        $termId = self::getActiveTermId();
        
        if ($termId) {
            return Term::find($termId);
        }
        
        // Fallback to database query and update session
        $activeTerm = Term::getActiveTerm();
        if ($activeTerm) {
            self::setActiveTerm($activeTerm);
        }
        
        return $activeTerm;
    }

    /**
     * Set the active term in session.
     */
    public static function setActiveTerm(Term $term): void
    {
        Session::put('active_term_id', $term->id);
        Session::put('active_term_name', $term->name);
    }

    /**
     * Clear the active term from session.
     */
    public static function clearActiveTerm(): void
    {
        Session::forget('active_term_id');
        Session::forget('active_term_name');
    }

    /**
     * Refresh the active term in session.
     * Useful when term data changes.
     */
    public static function refreshActiveTerm(): void
    {
        self::clearActiveTerm();
        
        $activeTerm = Term::getActiveTerm();
        if ($activeTerm) {
            self::setActiveTerm($activeTerm);
        }
    }

    /**
     * Check if there's an active term set in session.
     */
    public static function hasActiveTerm(): bool
    {
        return Session::has('active_term_id');
    }
}
