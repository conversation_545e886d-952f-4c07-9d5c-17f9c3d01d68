@extends('layouts.student')

@section('title', __('student.offline_title'))

@section('content')
<div class="game-container flex items-center justify-center min-h-screen">
    <div class="text-center max-w-md">
        <!-- Offline Icon -->
        <div class="mb-8">
            <div class="achievement-badge bg-game-red mx-auto mb-4" style="width: 100px; height: 100px; font-size: 40px;">
                📡
            </div>
            <h1 class="text-3xl font-black text-white mb-2">
                {{ __('student.offline_title') }}
            </h1>
            <p class="text-white text-lg font-semibold opacity-90">
                {{ __('student.offline_subtitle') }}
            </p>
        </div>

        <!-- Offline Message -->
        <div class="game-card p-8 mb-6">
            <div class="text-center">
                <div class="achievement-badge bg-game-orange mx-auto mb-4" style="width: 60px; height: 60px; font-size: 24px;">
                    🌐
                </div>
                <h2 class="text-xl font-black text-gray-800 mb-3">
                    {{ __('student.no_internet') }}
                </h2>
                <p class="text-gray-600 font-semibold mb-6">
                    {{ __('student.offline_description') }}
                </p>
                
                <!-- Retry Button -->
                <button class="game-btn w-full mb-4" onclick="retryConnection()">
                    <span id="retry-text">{{ __('student.retry_connection') }}</span>
                    <div id="retry-loading" class="loading-dots hidden ml-2">
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                    </div>
                </button>
                
                <!-- Auto-retry status -->
                <p class="text-sm text-gray-500 font-semibold">
                    {{ __('student.auto_retry') }} <span id="countdown">30</span>s
                </p>
            </div>
        </div>

        <!-- Offline Tips -->
        <div class="game-card p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200">
            <div class="text-center">
                <div class="achievement-badge bg-game-blue mx-auto mb-3" style="width: 40px; height: 40px; font-size: 16px;">
                    💡
                </div>
                <h3 class="text-lg font-black text-gray-800 mb-3">
                    {{ __('student.troubleshooting_tips') }}
                </h3>
                <div class="text-left space-y-2">
                    <div class="flex items-start">
                        <div class="achievement-badge bg-game-green mr-3 mt-1" style="width: 20px; height: 20px; font-size: 10px;">
                            ✓
                        </div>
                        <p class="text-sm text-gray-700 font-semibold">
                            {{ __('student.check_wifi') }}
                        </p>
                    </div>
                    <div class="flex items-start">
                        <div class="achievement-badge bg-game-green mr-3 mt-1" style="width: 20px; height: 20px; font-size: 10px;">
                            ✓
                        </div>
                        <p class="text-sm text-gray-700 font-semibold">
                            {{ __('student.check_mobile_data') }}
                        </p>
                    </div>
                    <div class="flex items-start">
                        <div class="achievement-badge bg-game-green mr-3 mt-1" style="width: 20px; height: 20px; font-size: 10px;">
                            ✓
                        </div>
                        <p class="text-sm text-gray-700 font-semibold">
                            {{ __('student.restart_browser') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fun Offline Activity -->
        <div class="game-card p-6 mt-6 bg-gradient-to-r from-green-50 to-blue-50 border-2 border-green-200">
            <div class="text-center">
                <div class="achievement-badge bg-game-green mx-auto mb-3" style="width: 40px; height: 40px; font-size: 16px;">
                    🎮
                </div>
                <h3 class="text-lg font-black text-gray-800 mb-2">
                    {{ __('student.offline_activity') }}
                </h3>
                <p class="text-sm text-gray-700 font-semibold mb-4">
                    {{ __('student.offline_activity_description') }}
                </p>
                <button class="game-btn green" onclick="startOfflineActivity()">
                    {{ __('student.start_activity') }}
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    let retryCountdown = 30;
    let countdownInterval;
    let autoRetryTimeout;

    // Retry connection
    function retryConnection() {
        const retryText = document.getElementById('retry-text');
        const retryLoading = document.getElementById('retry-loading');
        
        retryText.textContent = '{{ __('student.checking_connection') }}';
        retryLoading.classList.remove('hidden');
        
        hapticFeedback('medium');
        
        // Simulate connection check
        setTimeout(() => {
            if (navigator.onLine) {
                showToast('{{ __('student.connection_restored') }}', 'success');
                hapticFeedback('success');
                
                // Redirect back to dashboard
                setTimeout(() => {
                    window.location.href = '{{ route('student.dashboard') }}';
                }, 1000);
            } else {
                retryText.textContent = '{{ __('student.retry_connection') }}';
                retryLoading.classList.add('hidden');
                showToast('{{ __('student.still_offline') }}', 'error');
                hapticFeedback('error');
                
                // Reset countdown
                resetCountdown();
            }
        }, 2000);
    }

    // Start countdown for auto-retry
    function startCountdown() {
        const countdownElement = document.getElementById('countdown');
        
        countdownInterval = setInterval(() => {
            retryCountdown--;
            countdownElement.textContent = retryCountdown;
            
            if (retryCountdown <= 0) {
                clearInterval(countdownInterval);
                retryConnection();
            }
        }, 1000);
    }

    // Reset countdown
    function resetCountdown() {
        retryCountdown = 30;
        document.getElementById('countdown').textContent = retryCountdown;
        clearInterval(countdownInterval);
        startCountdown();
    }

    // Start offline activity
    function startOfflineActivity() {
        hapticFeedback('success');
        showToast('{{ __('student.activity_started') }}', 'success');
        
        // Simple offline word game placeholder
        const words = [
            '{{ __('student.word_book') }}',
            '{{ __('student.word_read') }}',
            '{{ __('student.word_learn') }}',
            '{{ __('student.word_story') }}',
            '{{ __('student.word_adventure') }}'
        ];
        
        const randomWord = words[Math.floor(Math.random() * words.length)];
        showToast(`{{ __('student.spell_word') }}: ${randomWord}`, 'success');
    }

    // Check online status
    function checkOnlineStatus() {
        if (navigator.onLine) {
            showToast('{{ __('student.back_online') }}', 'success');
            setTimeout(() => {
                window.location.href = '{{ route('student.dashboard') }}';
            }, 1000);
        }
    }

    // Initialize offline page
    document.addEventListener('DOMContentLoaded', function() {
        startCountdown();
        
        // Listen for online/offline events
        window.addEventListener('online', checkOnlineStatus);
        window.addEventListener('offline', function() {
            showToast('{{ __('student.connection_lost') }}', 'error');
        });
        
        // Check connection status every 10 seconds
        setInterval(checkOnlineStatus, 10000);
    });

    // Clean up intervals when leaving page
    window.addEventListener('beforeunload', function() {
        if (countdownInterval) {
            clearInterval(countdownInterval);
        }
        if (autoRetryTimeout) {
            clearTimeout(autoRetryTimeout);
        }
    });
</script>
@endpush
@endsection
