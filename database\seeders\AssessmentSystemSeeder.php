<?php

namespace Database\Seeders;

use App\Models\Book;
use App\Models\BookQuestion;
use App\Models\BookWord;
use App\Models\BookActivityType;
use App\Models\Program;
use App\Models\User;
use App\Services\AssessmentService;
use Illuminate\Database\Seeder;

class AssessmentSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding assessment system...');
        
        // Get first book for sample data
        $book = Book::first();
        
        if (!$book) {
            $this->command->warn('No books found. Please seed books first.');
            return;
        }
        
        // Create activity types
        $this->createActivityTypes();
        
        // Create sample questions for the book
        $this->createSampleQuestions($book);
        
        // Create sample vocabulary words
        $this->createSampleWords($book);
        
        // Create sample quizzes and activities
        $this->createSampleAssessments($book);
        
        $this->command->info('Assessment system seeding completed successfully!');
    }
    
    /**
     * Create activity types.
     */
    private function createActivityTypes(): void
    {
        $activityTypes = [
            [
                'category' => BookActivityType::CATEGORY_VOCABULARY,
                'name' => 'Word Definition Match',
                'description' => 'Match vocabulary words with their correct definitions.',
                'points_base' => 15,
            ],
            [
                'category' => BookActivityType::CATEGORY_VOCABULARY,
                'name' => 'Synonym and Antonym Exercise',
                'description' => 'Find synonyms and antonyms for given vocabulary words.',
                'points_base' => 20,
            ],
            [
                'category' => BookActivityType::CATEGORY_WRITING,
                'name' => 'Character Analysis Essay',
                'description' => 'Write a detailed analysis of a main character from the book.',
                'min_word_count' => 200,
                'max_word_count' => 500,
                'points_base' => 50,
            ],
            [
                'category' => BookActivityType::CATEGORY_WRITING,
                'name' => 'Book Summary',
                'description' => 'Write a comprehensive summary of the book.',
                'min_word_count' => 150,
                'max_word_count' => 300,
                'points_base' => 40,
            ],
            [
                'category' => BookActivityType::CATEGORY_COMPREHENSION,
                'name' => 'Plot Analysis',
                'description' => 'Analyze the main plot points and story structure.',
                'points_base' => 30,
            ],
            [
                'category' => BookActivityType::CATEGORY_CREATIVE,
                'name' => 'Alternative Ending',
                'description' => 'Write an alternative ending for the story.',
                'min_word_count' => 100,
                'max_word_count' => 250,
                'points_base' => 35,
            ],
            [
                'category' => BookActivityType::CATEGORY_SPELLING,
                'name' => 'Vocabulary Spelling Test',
                'description' => 'Spell vocabulary words correctly from audio prompts.',
                'points_base' => 25,
            ],
        ];
        
        foreach ($activityTypes as $typeData) {
            $existing = BookActivityType::where('category', $typeData['category'])
                                       ->where('name', $typeData['name'])
                                       ->first();

            if (!$existing) {
                BookActivityType::create($typeData);
                $this->command->info("Created activity type: {$typeData['name']}");
            } else {
                $this->command->info("Activity type already exists: {$typeData['name']}");
            }
        }
    }
    
    /**
     * Create sample questions for a book.
     */
    private function createSampleQuestions(Book $book): void
    {
        $questions = [
            [
                'question_text' => 'Who is the main character of the story?',
                'correct_answer' => 'Alice',
                'incorrect_answer_1' => 'Bob',
                'incorrect_answer_2' => 'Charlie',
                'incorrect_answer_3' => 'Diana',
                'difficulty_level' => 'easy',
                'page_start' => 1,
                'page_end' => 10,
            ],
            [
                'question_text' => 'What is the main theme of the book?',
                'correct_answer' => 'Friendship and courage',
                'incorrect_answer_1' => 'Love and romance',
                'incorrect_answer_2' => 'War and conflict',
                'incorrect_answer_3' => 'Mystery and suspense',
                'difficulty_level' => 'medium',
            ],
            [
                'question_text' => 'In which setting does most of the story take place?',
                'correct_answer' => 'A magical forest',
                'incorrect_answer_1' => 'A modern city',
                'incorrect_answer_2' => 'An ancient castle',
                'incorrect_answer_3' => 'A space station',
                'difficulty_level' => 'easy',
                'page_start' => 15,
                'page_end' => 25,
            ],
            [
                'question_text' => 'What motivates the protagonist\'s journey?',
                'correct_answer' => 'To save their village from danger',
                'incorrect_answer_1' => 'To find treasure',
                'incorrect_answer_2' => 'To become famous',
                'incorrect_answer_3' => 'To learn magic',
                'difficulty_level' => 'medium',
                'page_start' => 30,
                'page_end' => 50,
            ],
            [
                'question_text' => 'How does the conflict in the story get resolved?',
                'correct_answer' => 'Through teamwork and sacrifice',
                'incorrect_answer_1' => 'Through violence and force',
                'incorrect_answer_2' => 'Through magic spells',
                'incorrect_answer_3' => 'Through negotiation',
                'difficulty_level' => 'hard',
                'page_start' => 80,
                'page_end' => 100,
            ],
            [
                'question_text' => 'What lesson does the main character learn?',
                'correct_answer' => 'The importance of believing in yourself',
                'incorrect_answer_1' => 'Money is the most important thing',
                'incorrect_answer_2' => 'Never trust anyone',
                'incorrect_answer_3' => 'Power corrupts everyone',
                'difficulty_level' => 'hard',
            ],
            [
                'question_text' => 'Which character serves as the mentor figure?',
                'correct_answer' => 'The wise old wizard',
                'incorrect_answer_1' => 'The young prince',
                'incorrect_answer_2' => 'The evil queen',
                'incorrect_answer_3' => 'The talking animal',
                'difficulty_level' => 'medium',
                'page_start' => 20,
                'page_end' => 30,
            ],
            [
                'question_text' => 'What is the climax of the story?',
                'correct_answer' => 'The final battle against the dark forces',
                'incorrect_answer_1' => 'The discovery of the magic sword',
                'incorrect_answer_2' => 'The meeting with the wizard',
                'incorrect_answer_3' => 'The return to the village',
                'difficulty_level' => 'hard',
                'page_start' => 75,
                'page_end' => 85,
            ],
        ];
        
        foreach ($questions as $questionData) {
            $questionData['book_id'] = $book->id;

            $existing = BookQuestion::where('book_id', $book->id)
                                   ->where('question_text', $questionData['question_text'])
                                   ->first();

            if (!$existing) {
                BookQuestion::create($questionData);
            }
        }
        
        $this->command->info("Created " . count($questions) . " sample questions for book: {$book->name}");
    }
    
    /**
     * Create sample vocabulary words.
     */
    private function createSampleWords(Book $book): void
    {
        $words = [
            [
                'word' => 'Adventure',
                'definition' => 'An exciting or unusual experience or activity',
                'synonym' => 'Journey',
                'antonym' => 'Routine',
                'page_reference' => 5,
                'difficulty_level' => 'easy',
            ],
            [
                'word' => 'Courage',
                'definition' => 'The ability to do something that frightens one; bravery',
                'synonym' => 'Bravery',
                'antonym' => 'Cowardice',
                'page_reference' => 12,
                'difficulty_level' => 'medium',
            ],
            [
                'word' => 'Mysterious',
                'definition' => 'Difficult or impossible to understand, explain, or identify',
                'synonym' => 'Enigmatic',
                'antonym' => 'Clear',
                'page_reference' => 18,
                'difficulty_level' => 'medium',
            ],
            [
                'word' => 'Enchanted',
                'definition' => 'Under a spell; having magical properties',
                'synonym' => 'Magical',
                'antonym' => 'Ordinary',
                'page_reference' => 25,
                'difficulty_level' => 'easy',
            ],
            [
                'word' => 'Perilous',
                'definition' => 'Full of danger or risk',
                'synonym' => 'Dangerous',
                'antonym' => 'Safe',
                'page_reference' => 35,
                'difficulty_level' => 'hard',
            ],
            [
                'word' => 'Wisdom',
                'definition' => 'The quality of having experience, knowledge, and good judgment',
                'synonym' => 'Intelligence',
                'antonym' => 'Ignorance',
                'page_reference' => 42,
                'difficulty_level' => 'medium',
            ],
            [
                'word' => 'Treacherous',
                'definition' => 'Guilty of or involving betrayal or deception',
                'synonym' => 'Deceitful',
                'antonym' => 'Loyal',
                'page_reference' => 55,
                'difficulty_level' => 'hard',
            ],
            [
                'word' => 'Triumph',
                'definition' => 'A great victory or achievement',
                'synonym' => 'Victory',
                'antonym' => 'Defeat',
                'page_reference' => 78,
                'difficulty_level' => 'medium',
            ],
        ];
        
        foreach ($words as $wordData) {
            $wordData['book_id'] = $book->id;

            $existing = BookWord::where('book_id', $book->id)
                               ->where('word', $wordData['word'])
                               ->first();

            if (!$existing) {
                BookWord::create($wordData);
            }
        }
        
        $this->command->info("Created " . count($words) . " sample vocabulary words for book: {$book->name}");
    }
    
    /**
     * Create sample assessments.
     */
    private function createSampleAssessments(Book $book): void
    {
        $program = Program::where('is_active', true)->first();
        $students = User::whereHas('termUsers', function($query) {
            $query->whereHas('role', function($roleQuery) {
                $roleQuery->where('level', 5); // Student level
            });
        })->limit(3)->get();
        
        if (!$program || $students->isEmpty()) {
            $this->command->warn('No active program or students found for sample assessments.');
            return;
        }
        
        $assessmentService = new AssessmentService();
        
        // Create sample quizzes
        foreach ($students as $student) {
            // Create a completion quiz
            $quiz = $assessmentService->generateCompletionQuiz(
                $program->id,
                $book->id,
                $student->id,
                5, // 5 questions
                60.0, // 60% passing score
                30 // 30 minutes time limit
            );
            
            $this->command->info("Created completion quiz for student: {$student->name}");
            
            // Create a daily reading quiz
            $dailyQuiz = $assessmentService->generateDailyReadingQuiz(
                $program->id,
                $book->id,
                $student->id,
                1, // Page start
                20, // Page end
                3 // 3 questions
            );
            
            $this->command->info("Created daily reading quiz for student: {$student->name}");
            
            // Create sample activities
            $activityTypes = BookActivityType::active()->limit(2)->get();
            foreach ($activityTypes as $activityType) {
                $activity = $assessmentService->createVocabularyActivity(
                    $program->id,
                    $book->id,
                    $student->id,
                    $activityType->id
                );
                
                $this->command->info("Created {$activityType->name} activity for student: {$student->name}");
            }
        }
        
        // Display statistics
        $stats = $assessmentService->getAssessmentStatistics($program->id, $book->id);
        $readiness = $assessmentService->getBookAssessmentReadiness($book->id);
        
        $this->command->info('Assessment Statistics:');
        $this->command->info("- Total Quizzes: {$stats['quizzes']['total']}");
        $this->command->info("- Total Activities: {$stats['activities']['total_activities']}");
        $this->command->info("- Book Assessment Ready: " . ($readiness['ready_for_assessment'] ? 'Yes' : 'No'));
        $this->command->info("- Available Questions: {$readiness['questions']['total']}");
        $this->command->info("- Available Words: {$readiness['words']['total']}");
    }
}
