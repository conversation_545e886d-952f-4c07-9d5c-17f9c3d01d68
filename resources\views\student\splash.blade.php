@extends('layouts.student')

@section('title', __('student.app_title'))

@section('content')
<div class="game-container flex items-center justify-center min-h-screen">
    <div class="text-center">
        <!-- App Logo -->
        <div class="splash-logo mb-8">
            <div class="achievement-badge bg-game-blue mx-auto mb-4" style="width: 120px; height: 120px; font-size: 48px;">
                📚
            </div>
            <h1 class="text-4xl font-black text-white mb-2">
                {{ __('student.app_name') }}
            </h1>
            <p class="text-white text-lg font-semibold opacity-90">
                {{ __('student.app_subtitle') }}
            </p>
        </div>

        <!-- Loading Animation -->
        <div class="mb-8">
            <div class="loading-dots text-white text-xl">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
            </div>
            <p class="text-white font-semibold mt-4">
                {{ __('student.loading_game') }}
            </p>
        </div>

        <!-- Fun Facts -->
        <div class="game-card p-6 max-w-sm mx-auto">
            <div class="text-center">
                <div class="achievement-badge bg-game-green mx-auto mb-3" style="width: 40px; height: 40px; font-size: 16px;">
                    💡
                </div>
                <p class="text-gray-700 font-semibold text-sm" id="fun-fact">
                    {{ __('student.loading_tip_1') }}
                </p>
            </div>
        </div>

        <!-- Continue Button (hidden initially) -->
        <div class="mt-8">
            <button id="continue-btn" class="game-btn green hidden" onclick="continueToLogin()">
                {{ __('student.continue') }}
            </button>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Fun facts to rotate through
    const funFacts = [
        "{{ __('student.loading_tip_1') }}",
        "{{ __('student.loading_tip_2') }}",
        "{{ __('student.loading_tip_3') }}",
        "{{ __('student.loading_tip_4') }}",
        "{{ __('student.loading_tip_5') }}"
    ];

    let currentFactIndex = 0;
    let factInterval;

    // Rotate through fun facts
    function rotateFunFacts() {
        const factElement = document.getElementById('fun-fact');
        factInterval = setInterval(() => {
            currentFactIndex = (currentFactIndex + 1) % funFacts.length;
            factElement.style.opacity = '0';
            setTimeout(() => {
                factElement.textContent = funFacts[currentFactIndex];
                factElement.style.opacity = '1';
            }, 300);
        }, 2000);
    }

    // Continue to login
    function continueToLogin() {
        hapticFeedback('success');
        showLoading();
        setTimeout(() => {
            window.location.href = '{{ route('student.login') }}';
        }, 500);
    }

    // Initialize splash screen
    document.addEventListener('DOMContentLoaded', function() {
        // Start rotating fun facts
        rotateFunFacts();
        
        // Show continue button after 3 seconds
        setTimeout(() => {
            document.getElementById('continue-btn').classList.remove('hidden');
            hapticFeedback('light');
        }, 3000);
        
        // Auto-continue after 8 seconds if user doesn't interact
        setTimeout(() => {
            if (!document.getElementById('continue-btn').classList.contains('hidden')) {
                continueToLogin();
            }
        }, 8000);
    });

    // Clean up interval when leaving page
    window.addEventListener('beforeunload', function() {
        if (factInterval) {
            clearInterval(factInterval);
        }
    });
</script>
@endpush
@endsection
