# Privacy Agreement Consent System

## Overview

This document describes the implementation of a privacy agreement consent system for first-time user logins using Moonshine's authentication pipelines feature. The system ensures that users must accept the current privacy policy before accessing the application.

## Features

- ✅ **Database Structure**: `user_agreements` table to store consent records
- ✅ **Authentication Pipeline**: Intercepts login process to check for consent
- ✅ **Privacy Agreement Page**: Displays privacy policy with mandatory consent
- ✅ **Client-side Validation**: JavaScript validation for consent checkbox
- ✅ **Server-side Validation**: Laravel validation for consent processing
- ✅ **Localization Support**: Turkish/English translations
- ✅ **Audit Trail**: IP address and timestamp recording
- ✅ **Edge Case Handling**: Multiple login attempts, bypass prevention
- ✅ **Admin Interface**: Moonshine resource for managing user agreements

## Database Structure

### `user_agreements` Table

```sql
CREATE TABLE user_agreements (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    agreement_type VARCHAR(255) DEFAULT 'privacy_policy',
    version VARCHAR(255) DEFAULT '1.0',
    accepted_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45) NULL,
    created_by BIGINT NULL,
    updated_by BIGINT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_agreement_type (user_id, agreement_type),
    INDEX idx_agreement_type_version (agreement_type, version),
    INDEX idx_accepted_at (accepted_at)
);
```

## Implementation Components

### 1. UserAgreement Model (`app/Models/UserAgreement.php`)

**Key Features:**
- Extends `BaseModel` for audit fields
- Soft deletes support
- Agreement type constants
- Version tracking
- Utility methods for checking and recording consent

**Important Methods:**
- `hasAcceptedCurrentPrivacyPolicy(int $userId): bool`
- `recordPrivacyPolicyAcceptance(int $userId, string $ipAddress): self`
- `getAgreementTypes(): array`

### 2. Authentication Pipeline (`app/MoonShine/AuthPipelines/PrivacyAgreementPipe.php`)

**Functionality:**
- Intercepts authenticated requests
- Checks if user has accepted current privacy policy
- Redirects to privacy agreement page if not accepted
- Stores intended URL for post-consent redirect
- Skips API requests and specific routes

**Skip Conditions:**
- JSON/API requests (`$request->expectsJson()`)
- Already on privacy agreement pages
- Logout requests

### 3. Privacy Agreement Page (`app/MoonShine/Pages/PrivacyAgreementPage.php`)

**Components:**
- Privacy policy content display
- Mandatory consent checkbox
- Client-side JavaScript validation
- Server-side form processing
- Error handling and success messages

**Validation Rules:**
- `privacy_consent` field must be `required|accepted`
- IP address recording for audit purposes

### 4. Admin Resource (`app/MoonShine/Resources/UserAgreementResource.php`)

**Features:**
- View-only access (no create/edit)
- Filtering by agreement type, version, user
- Search functionality
- Audit information display
- Relationship with User model

## Configuration

### 1. Moonshine Configuration (`config/moonshine.php`)

```php
'auth' => [
    'pipelines' => [
        \App\MoonShine\AuthPipelines\PrivacyAgreementPipe::class,
    ],
],

'pages' => [
    'privacy-agreement' => App\MoonShine\Pages\PrivacyAgreementPage::class,
],
```

### 2. Routes (`routes/web.php`)

```php
Route::middleware(['web'])->group(function () {
    Route::get('/admin/privacy-agreement', [PrivacyAgreementPage::class, 'handle'])
        ->name('moonshine.privacy-agreement');
    
    Route::post('/admin/process-privacy-consent', [PrivacyAgreementPage::class, 'processPrivacyConsent'])
        ->name('moonshine.process-privacy-consent');
});
```

## Localization

### Translation Keys

**English (`lang/en/admin.php`):**
- `privacy_agreement_title` - "Privacy Policy Agreement"
- `privacy_agreement_consent` - "I accept the privacy policy and terms of use"
- `accept_and_continue` - "Accept and Continue"
- `privacy_consent_required` - "You must accept the privacy policy to continue."

**Turkish (`lang/tr/admin.php`):**
- `privacy_agreement_title` - "Gizlilik Politikası Sözleşmesi"
- `privacy_agreement_consent` - "Gizlilik politikasını ve kullanım şartlarını kabul ediyorum"
- `accept_and_continue` - "Kabul Et ve Devam Et"
- `privacy_consent_required` - "Devam etmek için gizlilik politikasını kabul etmelisiniz."

## Security Features

### 1. CSRF Protection
- All forms include CSRF tokens
- Laravel's built-in CSRF middleware protection

### 2. Input Validation
- Server-side validation for all form inputs
- Client-side validation for user experience
- IP address validation and sanitization

### 3. Audit Trail
- IP address recording for each consent
- Timestamp tracking
- User identification
- Version tracking for policy changes

### 4. Bypass Prevention
- Pipeline checks all authenticated requests
- Specific route exclusions prevent infinite redirects
- Session-based intended URL storage

## Edge Cases Handled

### 1. Multiple Login Attempts
- Each login triggers pipeline check
- Existing consent prevents re-prompting
- Session management for intended URLs

### 2. API Requests
- JSON requests bypass privacy agreement
- JWT authentication compatibility maintained
- API endpoints remain functional

### 3. Version Updates
- New policy versions require new consent
- Historical consent records maintained
- Version comparison logic

### 4. Database Errors
- Try-catch blocks for consent recording
- Error logging for debugging
- Graceful error handling with user feedback

## Usage Examples

### Check User Consent Status
```php
$user = Auth::user();
if ($user->hasAcceptedCurrentPrivacyPolicy()) {
    // User has accepted current privacy policy
} else {
    // User needs to accept privacy policy
}
```

### Record Manual Consent
```php
UserAgreement::recordPrivacyPolicyAcceptance(
    $userId, 
    $request->ip()
);
```

### Query User Agreements
```php
// Get all privacy policy agreements
$privacyAgreements = UserAgreement::ofType(UserAgreement::TYPE_PRIVACY_POLICY)->get();

// Get recent agreements (last 30 days)
$recentAgreements = UserAgreement::recent(30)->get();

// Get agreements for specific version
$v1Agreements = UserAgreement::ofVersion('1.0')->get();
```

## Testing

### Unit Tests (`tests/Unit/UserAgreementTest.php`)
- Agreement type constants validation
- Model fillable fields verification
- Basic functionality testing

### Manual Testing Checklist
1. ✅ First-time user login redirects to privacy agreement
2. ✅ Privacy agreement page displays correctly
3. ✅ Checkbox validation works (client-side)
4. ✅ Form submission requires consent (server-side)
5. ✅ Successful consent redirects to intended URL
6. ✅ Subsequent logins skip privacy agreement
7. ✅ API requests bypass privacy agreement
8. ✅ Admin interface shows user agreements
9. ✅ Localization works for both languages
10. ✅ Audit trail records IP and timestamp

## Maintenance

### Updating Privacy Policy Version
1. Update `UserAgreement::CURRENT_PRIVACY_VERSION` constant
2. Update privacy policy content in `PrivacyAgreementPage`
3. All users will be prompted to accept new version

### Adding New Agreement Types
1. Add new type constant to `UserAgreement` model
2. Update `getAgreementTypes()` method
3. Add translation keys
4. Create new pipeline if needed

## Troubleshooting

### Common Issues
1. **Infinite redirect loops**: Check route exclusions in pipeline
2. **API requests failing**: Ensure JSON requests are properly excluded
3. **Translation missing**: Verify translation keys in language files
4. **Database errors**: Check foreign key constraints and table structure

### Debug Information
- Check Laravel logs for consent recording errors
- Verify session data for intended URLs
- Monitor database for consent records
- Test with different user roles and permissions
