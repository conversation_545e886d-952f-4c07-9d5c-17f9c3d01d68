<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use App\Models\Term;
use App\Models\TermUser;
use App\Models\Organization;
use App\Models\SchoolClass;
use App\Models\GradeLevel;
use Illuminate\Support\Facades\Hash;

class RoleBasedMenuTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles (without created_by/updated_by to avoid foreign key issues)
        $systemAdminRole = Role::create([
            'name' => 'System Administrator',
            'description' => 'System Administrator with full access',
            'level' => Role::LEVEL_SYSTEM_ADMIN,
        ]);

        $groupSchoolAdminRole = Role::create([
            'name' => 'Group School Administrator',
            'description' => 'Group School Administrator',
            'level' => Role::LEVEL_GROUP_SCHOOL_ADMIN,
        ]);

        $schoolAdminRole = Role::create([
            'name' => 'School Administrator',
            'description' => 'School Administrator',
            'level' => Role::LEVEL_SCHOOL_ADMIN,
        ]);

        $teacherRole = Role::create([
            'name' => 'Teacher',
            'description' => 'Teacher',
            'level' => Role::LEVEL_TEACHER,
        ]);

        $studentRole = Role::create([
            'name' => 'Student',
            'description' => 'Student',
            'level' => Role::LEVEL_STUDENT,
        ]);

        // Create organization
        $organization = Organization::create([
            'name' => 'Test School',
            'type' => 'school',
            'active' => true,
        ]);

        // Create grade level
        $gradeLevel = GradeLevel::create([
            'name' => 'Grade 1',
            'level' => 1,
        ]);

        // Create school class
        $schoolClass = SchoolClass::create([
            'name' => 'Class A',
            'organization_id' => $organization->id,
            'grade_level_id' => $gradeLevel->id,
        ]);

        // Create active term
        $term = Term::create([
            'name' => 'Test Term 2024',
            'start_date' => now()->subMonth(),
            'end_date' => now()->addMonth(),
            'active' => true,
        ]);

        // Create users
        $systemAdmin = User::create([
            'name' => 'System Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $systemAdminRole->id,
        ]);

        $schoolAdmin = User::create([
            'name' => 'School Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $schoolAdminRole->id,
        ]);

        $teacher = User::create([
            'name' => 'Teacher User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $teacherRole->id,
        ]);

        $student1 = User::create([
            'name' => 'Student One',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $studentRole->id,
        ]);

        $student2 = User::create([
            'name' => 'Student Two',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $studentRole->id,
        ]);

        $student3 = User::create([
            'name' => 'Student Three',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $studentRole->id,
        ]);

        // Create term user assignments
        // Assign teacher to class
        TermUser::create([
            'user_id' => $teacher->id,
            'role_id' => $teacherRole->id,
            'organization_id' => $organization->id,
            'class_id' => $schoolClass->id,
            'term_id' => $term->id,
            'active' => true,
        ]);

        // Assign students to class (only student1 and student2)
        TermUser::create([
            'user_id' => $student1->id,
            'role_id' => $studentRole->id,
            'organization_id' => $organization->id,
            'class_id' => $schoolClass->id,
            'term_id' => $term->id,
            'active' => true,
        ]);

        TermUser::create([
            'user_id' => $student2->id,
            'role_id' => $studentRole->id,
            'organization_id' => $organization->id,
            'class_id' => $schoolClass->id,
            'term_id' => $term->id,
            'active' => true,
        ]);

        // student3 is not assigned to any class (for testing filtering)

        $this->command->info('Role-based menu test data created successfully!');
        $this->command->info('Users created:');
        $this->command->info("- System Admin: <EMAIL> / password");
        $this->command->info("- School Admin: <EMAIL> / password");
        $this->command->info("- Teacher: <EMAIL> / password");
        $this->command->info("- Students: <EMAIL>, <EMAIL>, <EMAIL> / password");
        $this->command->info('');
        $this->command->info('Teacher is assigned to "Class A" with student1 and student2.');
        $this->command->info('Student3 is not assigned to any class (for testing filtering).');
    }
}
