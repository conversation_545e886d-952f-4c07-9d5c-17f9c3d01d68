<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class HandleApiRequests
{
    public function handle(Request $request, Closure $next): Response
    {
        // If this is an API request (Accept: application/json)
        if ($request->expectsJson()) {
            // Add the request to the VerifyCsrfToken exception list dynamically
            app('Illuminate\Foundation\Http\Middleware\VerifyCsrfToken')
                ->addUri($request->path());
        }

        return $next($request);
    }
}