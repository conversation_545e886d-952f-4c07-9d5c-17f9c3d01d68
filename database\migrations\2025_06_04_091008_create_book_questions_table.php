<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('book_questions', function (Blueprint $table) {
            $table->id();
            
            // Core question information
            $table->foreignId('book_id')->constrained('books')->onDelete('cascade');
            $table->text('question_text')->comment('The question content');
            
            // Rich media support
            $table->string('question_image_url')->nullable()->comment('Optional image URL');
            $table->string('question_audio_url')->nullable()->comment('Optional audio URL');
            $table->string('question_video_url')->nullable()->comment('Optional video URL');
            
            // Answer options
            $table->string('correct_answer')->comment('The correct answer text');
            $table->string('incorrect_answer_1')->nullable()->comment('First incorrect option');
            $table->string('incorrect_answer_2')->nullable()->comment('Second incorrect option');
            $table->string('incorrect_answer_3')->nullable()->comment('Third incorrect option');
            $table->string('incorrect_answer_4')->nullable()->comment('Fourth incorrect option');
            $table->string('incorrect_answer_5')->nullable()->comment('Fifth incorrect option');
            
            // Page references
            $table->integer('page_start')->nullable()->comment('Starting page reference');
            $table->integer('page_end')->nullable()->comment('Ending page reference');
            
            // Question metadata
            $table->enum('difficulty_level', ['easy', 'medium', 'hard'])->default('medium');
            $table->boolean('is_active')->default(true)->comment('Whether question is available for use');
            
            // Audit fields
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Indexes for performance
            $table->index(['book_id', 'is_active']);
            $table->index(['book_id', 'difficulty_level']);
            $table->index(['page_start', 'page_end']);
            $table->index('difficulty_level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('book_questions');
    }
};
