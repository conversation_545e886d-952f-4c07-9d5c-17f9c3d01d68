<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Program;
use App\Models\ProgramUserPoint;
use App\Models\TermUser;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\TermUserResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;

/**
 * @extends BaseResource<ProgramUserPoint>
 */
class ProgramUserPointResource extends BaseResource
{
    protected string $model = ProgramUserPoint::class;

    protected array $with = ['program', 'termUser', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_user_points');
    }



    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            BelongsTo::make(__('admin.term_users'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name)
                ->sortable(),
            Text::make(__('admin.point_source'), 'point_source_display_name')
                ->badge('blue'),
            Number::make(__('admin.points'), 'points')
                ->badge('green'),
            Date::make(__('admin.earned_at'), 'earned_at')
                ->format('d.m.Y H:i')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.programs'), 'program', 
                    formatted: fn(Program $program) => $program->name,
                    resource: ProgramResource::class)
                    ->required()
                    ->placeholder(__('admin.select_program')),
                
                BelongsTo::make(__('admin.term_users'), 'termUser', 
                    formatted: fn(TermUser $termUser) => $termUser->user->name,
                    resource: TermUserResource::class)
                    ->required()
                    ->placeholder(__('admin.select_student')),
                
                Flex::make([
                    Select::make(__('admin.point_source'), 'point_source')
                        ->required()
                        ->options(ProgramUserPoint::getPointSources()),
                    
                    Number::make(__('admin.points'), 'points')
                        ->required()
                        ->min(1),
                ]),
                
                Date::make(__('admin.earned_at'), 'earned_at')
                    ->required()
                    ->default(now()),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name),
            BelongsTo::make(__('admin.term_users'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name),
            Text::make(__('admin.point_source'), 'point_source_display_name')
                ->badge('blue'),
            Number::make(__('admin.points'), 'points')
                ->badge('green'),
            Date::make(__('admin.earned_at'), 'earned_at')
                ->format('d.m.Y H:i'),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
            Select::make(__('admin.point_source'), 'point_source')
                ->options(ProgramUserPoint::getPointSources()),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'term_user_id' => ['required', 'exists:term_users,id'],
            'point_source' => ['required', 'integer', 'min:1', 'max:5'],
            'points' => ['required', 'integer', 'min:1'],
            'earned_at' => ['required', 'date'],
            ...parent::getCommonRules($item),
        ];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

}
