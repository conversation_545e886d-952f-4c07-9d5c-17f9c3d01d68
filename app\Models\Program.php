<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Program extends BaseModel
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'story_id',
        'start_date',
        'end_date',
        'is_active',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'start_date' => 'date',
            'end_date' => 'date',
            'is_active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the story this program is based on.
     */
    public function story(): BelongsTo
    {
        return $this->belongsTo(Story::class);
    }

    /**
     * Get the schools participating in this program.
     */
    public function schools(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'program_schools', 'program_id', 'organization_id')
                    ->withTimestamps()
                    ->withPivot(['created_by', 'updated_by', 'deleted_by', 'deleted_at']);
    }

    /**
     * Get the classes participating in this program.
     */
    public function classes(): BelongsToMany
    {
        return $this->belongsToMany(SchoolClass::class, 'program_classes', 'program_id', 'school_class_id')
                    ->withTimestamps()
                    ->withPivot(['created_by', 'updated_by', 'deleted_by', 'deleted_at']);
    }

    /**
     * Get the books associated with this program.
     */
    public function books(): BelongsToMany
    {
        return $this->belongsToMany(Book::class, 'program_books')
                    ->withTimestamps()
                    ->withPivot(['created_by', 'updated_by', 'deleted_by', 'deleted_at']);
    }

    /**
     * Get the teams for this program.
     */
    public function teams(): HasMany
    {
        return $this->hasMany(ProgramTeam::class);
    }

    /**
     * Get the user levels for this program.
     */
    public function userLevels(): HasMany
    {
        return $this->hasMany(ProgramUserLevel::class);
    }

    /**
     * Get the user achievements for this program.
     */
    public function userAchievements(): HasMany
    {
        return $this->hasMany(ProgramUserAchievement::class);
    }

    /**
     * Get the user characters for this program.
     */
    public function userCharacters(): HasMany
    {
        return $this->hasMany(ProgramUserCharacter::class);
    }

    /**
     * Get the user maps for this program.
     */
    public function userMaps(): HasMany
    {
        return $this->hasMany(ProgramUserMap::class);
    }

    /**
     * Get the user points for this program.
     */
    public function userPoints(): HasMany
    {
        return $this->hasMany(ProgramUserPoint::class);
    }

    /**
     * Get the tasks for this program.
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(ProgramTask::class);
    }

    /**
     * Get the book quizzes for this program.
     */
    public function bookQuizzes(): HasMany
    {
        return $this->hasMany(ProgramBookQuiz::class);
    }

    /**
     * Get the book activities for this program.
     */
    public function bookActivities(): HasMany
    {
        return $this->hasMany(ProgramBookActivity::class);
    }

    /**
     * Get the reading logs for this program.
     */
    public function readingLogs(): HasMany
    {
        return $this->hasMany(ProgramReadingLog::class);
    }

    /**
     * Get the user book assignments for this program.
     */
    public function userBooks(): HasMany
    {
        return $this->hasMany(ProgramUserBook::class);
    }

    /**
     * Scope to get active programs.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get current programs (within date range).
     */
    public function scopeCurrent($query)
    {
        $now = now()->toDateString();
        return $query->where('start_date', '<=', $now)
                    ->where('end_date', '>=', $now);
    }

    /**
     * Check if program is currently active and within date range.
     */
    public function getIsCurrentAttribute(): bool
    {
        $now = now()->toDateString();
        return $this->is_active && 
               $this->start_date <= $now && 
               $this->end_date >= $now;
    }

    /**
     * Get program duration in days.
     */
    public function getDurationAttribute(): int
    {
        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    /**
     * Get program status.
     */
    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        $now = now()->toDateString();
        
        if ($this->start_date > $now) {
            return 'upcoming';
        } elseif ($this->end_date < $now) {
            return 'completed';
        } else {
            return 'active';
        }
    }
}
