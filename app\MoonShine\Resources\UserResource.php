<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Log;
use MoonShine\Laravel\Enums\Ability;
use App\Models\Term;
use App\Models\TermUser;
use Illuminate\Contracts\Database\Eloquent\Builder;

use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Email;
use MoonShine\UI\Fields\Password;
use MoonShine\UI\Fields\PasswordRepeat;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;
use Sweet1s\MoonshineRBAC\Traits\WithRoleFormComponent;

#[Icon('users')]
class UserResource extends BaseResource
{
    use WithRolePermissions;
    use WithRoleFormComponent;

    //WARN: We modify getRoles method in vendor\sweet1s\moonshine-roles-permissions\src\FormComponents\RoleFormComponent.php to have role description
    // TODO: Teachers can only see their own students

    protected string $model = User::class;

    protected string $column = 'name';

    protected $guard_name = "moonshine";

    protected array $with = ['creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.users');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.name'), 'name')
                ->sortable(),

            Text::make(__('admin.title'), 'title')
                ->sortable(),
            
            Email::make(__('admin.email'), 'email')
                ->sortable(),            
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        Flex::make([
                            Text::make(__('admin.name'), 'name')
                                ->required()
                                ->placeholder(__('admin.enter_name')),

                    Text::make(__('admin.title'), 'title')
                        ->placeholder(__('admin.enter_title')),
                            
                            Email::make(__('admin.email'), 'email')
                                ->required()
                                ->placeholder(__('admin.enter_email')),
                        ]),                        

                    ]),
                    
                    Tab::make(__('admin.password'), [
                        Password::make(__('admin.password'), 'password')
                            ->customAttributes(['autocomplete' => 'new-password'])
                            ->required($this->isCreateFormPage()),
                        
                        PasswordRepeat::make(__('admin.password_repeat'), 'password_confirmation')
                            ->customAttributes(['autocomplete' => 'confirm-password'])
                            ->required($this->isCreateFormPage()),
                    ]),
                ]),
            ]),
            
            
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.title'), 'title'),
            Email::make(__('admin.email'), 'email'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'title' => ['nullable', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $item?->id],
            ...parent::getCommonRules($item),
        ];

        // Password rules
        if (!$item) { // Creating new user
            $rules['password'] = ['required', 'string', 'min:8', 'confirmed'];
        } elseif (request()->filled('password')) {
            $rules['password'] = ['string', 'min:8', 'confirmed'];
        }

        return $rules;
    }

    protected function getSearchFields(): array
    {
        return ['name', 'email'];
    }
}
