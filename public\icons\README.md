# Teacher App PWA Icons

This directory should contain the following icon files for the Teacher PWA:

## Required Icons:
- teacher-icon-72x72.png
- teacher-icon-96x96.png
- teacher-icon-128x128.png
- teacher-icon-144x144.png
- teacher-icon-152x152.png
- teacher-icon-192x192.png
- teacher-icon-384x384.png
- teacher-icon-512x512.png

## Shortcut Icons:
- dashboard-shortcut-96x96.png
- students-shortcut-96x96.png

## Notification Icons:
- teacher-badge-72x72.png
- checkmark.png
- xmark.png

## Screenshots (for app store listings):
- teacher-dashboard-mobile.png (375x812)
- teacher-students-mobile.png (375x812)

## Icon Guidelines:
- Use a consistent design theme with purple (#7843E9) as the primary color
- Icons should be clear and recognizable at small sizes
- Use a book or education-related symbol for the main app icon
- Ensure icons work well on both light and dark backgrounds
- Follow platform-specific guidelines for maskable icons

## Generating Icons:
You can use tools like:
- PWA Builder (https://www.pwabuilder.com/)
- Favicon Generator (https://realfavicongenerator.net/)
- Or create them manually using design software

## Note:
Currently using placeholder references. Replace with actual icon files before production deployment.
