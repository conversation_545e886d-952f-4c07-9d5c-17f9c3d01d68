<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class StudentAuthentication
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated with moonshine guard
        if (!Auth::guard('moonshine')->check()) {
            return redirect()->route('student.login');
        }

        $user = Auth::guard('moonshine')->user();

        // Check if user has student role (level 4)
        if (!$user->isStudent()) {
            Auth::guard('moonshine')->logout();
            return redirect()->route('student.login')
                ->withErrors(['error' => __('admin.student.access_denied')]);
        }

        return $next($request);
    }
}
