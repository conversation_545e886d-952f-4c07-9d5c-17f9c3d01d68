<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Helpers\ActiveTermHelper;

class TermUser extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'role_id',
        'organization_id',
        'class_id',
        'term_id',
        'active',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get the user.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the role.
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Get the organization.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the class.
     */
    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(SchoolClass::class, 'class_id');
    }

    /**
     * Get the term.
     */
    public function term(): BelongsTo
    {
        return $this->belongsTo(Term::class);
    }

    /**
     * Scope to get active assignments.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to filter by term.
     */
    public function scopeByTerm($query, $termId)
    {
        return $query->where('term_id', $termId);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by role.
     */
    public function scopeByRole($query, $roleId)
    {
        return $query->where('role_id', $roleId);
    }

    /**
     * Scope to filter by organization.
     */
    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Scope to filter by class.
     */
    public function scopeByClass($query, $classId)
    {
        return $query->where('class_id', $classId);
    }

    /**
     * Scope to get current term assignments.
     */
    public function scopeCurrentTerm($query)
    {
        $activeTermId = ActiveTermHelper::getActiveTermId();
        if ($activeTermId) {
            return $query->where('term_id', $activeTermId);
        }
        return $query->whereRaw('1 = 0'); // Return empty result
    }

    /**
     * Get the display name for this assignment.
     */
    public function getDisplayNameAttribute(): string
    {
        $parts = [
            $this->user->name,
            $this->role->name,
            $this->organization?->name,
        ];

        if ($this->schoolClass) {
            $parts[] = $this->schoolClass->full_name;
        }

        return implode(' - ', array_filter($parts));
    }

    /**
     * Get assignment summary.
     */
    public function getSummaryAttribute(): string
    {
        $summary = $this->user->name . ' as ' . $this->role->name;
        
        if ($this->organization) {
            $summary .= ' at ' . $this->organization->name;
        }
        
        if ($this->schoolClass) {
            $summary .= ' (Class: ' . $this->schoolClass->full_name . ')';
        }
        
        return $summary;
    }

    /**
     * Activate this assignment.
     */
    public function activate(): bool
    {
        return $this->update(['active' => true]);
    }

    /**
     * Deactivate this assignment.
     */
    public function deactivate(): bool
    {
        return $this->update(['active' => false]);
    }

    /**
     * Get the program user book assignments for this term user.
     */
    public function programUserBooks(): HasMany
    {
        return $this->hasMany(ProgramUserBook::class);
    }
}
