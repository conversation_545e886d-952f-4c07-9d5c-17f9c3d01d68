<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('program_reading_logs', function (Blueprint $table) {
            $table->id();
            
            // Core reading log information
            $table->foreignId('program_id')->constrained('programs')->onDelete('cascade');
            $table->foreignId('book_id')->constrained('books')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            
            // Task integration (optional)
            $table->foreignId('program_task_instance_id')->nullable()
                  ->constrained('program_task_instances')->onDelete('set null')
                  ->comment('If assigned as task');
            
            // Reading session details
            $table->date('reading_date')->comment('The date of reading session');
            $table->integer('start_page')->comment('Page where reading session started');
            $table->integer('end_page')->comment('Page where reading session ended');
            $table->integer('pages_read')->storedAs('end_page - start_page + 1')
                  ->comment('Calculated as end_page - start_page + 1');
            $table->integer('reading_duration_minutes')->nullable()
                  ->comment('Time spent reading in minutes');
            $table->decimal('reading_speed_pages_per_minute', 5, 2)->nullable()
                  ->comment('Calculated if duration provided');
            
            // Reading content
            $table->text('reading_notes')->nullable()->comment('Student\'s thoughts or summary');
            
            // Verification system
            $table->boolean('is_verified')->default(false)->comment('Teacher verification status');
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null')
                  ->comment('Teacher who verified');
            $table->datetime('verified_at')->nullable()->comment('When verification occurred');
            
            // Points and rewards
            $table->integer('points_awarded')->default(0)->comment('Points earned for this log entry');
            
            // Audit fields
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Indexes for performance
            $table->index(['user_id', 'reading_date'], 'user_date_idx');
            $table->index(['program_id', 'book_id'], 'program_book_idx');
            $table->index(['is_verified', 'verified_by'], 'verification_idx');
            $table->index(['reading_date', 'program_id'], 'date_program_idx');
            $table->index('program_task_instance_id');
            $table->index('points_awarded');
            
            // Unique constraint to prevent duplicate daily entries
            $table->unique(['program_id', 'book_id', 'user_id', 'reading_date'], 'unique_daily_log');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('program_reading_logs');
    }
};
