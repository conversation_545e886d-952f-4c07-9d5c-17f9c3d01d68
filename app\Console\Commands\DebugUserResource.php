<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;
use App\Models\Term;
use App\Models\TermUser;
use Illuminate\Support\Facades\Auth;

class DebugUserResource extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'debug:user-resource';

    /**
     * The console command description.
     */
    protected $description = 'Debug UserResource filtering for teacher';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Debug UserResource Filtering ===');
        $this->newLine();

        // Get teacher
        $teacher = User::whereHas('role', function($q) {
            $q->where('level', Role::LEVEL_TEACHER);
        })->first();

        if (!$teacher) {
            $this->error('No teacher found!');
            return 1;
        }

        $this->info("Teacher: {$teacher->name} (ID: {$teacher->id})");
        $this->info("Role: {$teacher->role->name} (Level: {$teacher->role->level})");
        $this->newLine();

        // Check active term
        $activeTerm = Term::getActiveTerm();
        if (!$activeTerm) {
            $this->error('No active term found!');
            return 1;
        }

        $this->info("Active Term: {$activeTerm->name} (ID: {$activeTerm->id})");
        $this->newLine();

        // Check teacher's class assignments
        $teacherTermUsers = TermUser::where('user_id', $teacher->id)
            ->where('term_id', $activeTerm->id)
            ->where('active', true)
            ->with(['role', 'schoolClass'])
            ->get();

        $this->info("Teacher's Term Assignments:");
        foreach ($teacherTermUsers as $termUser) {
            $this->line("  - Class: " . ($termUser->schoolClass ? $termUser->schoolClass->name : 'NULL'));
            $this->line("    Role: " . ($termUser->role ? $termUser->role->name : 'NULL'));
            $this->line("    Role Level: " . ($termUser->role ? $termUser->role->level : 'NULL'));
            $this->line("    Active: " . ($termUser->active ? 'YES' : 'NO'));
        }
        $this->newLine();

        // Get teacher's assigned classes
        $teacherClassIds = TermUser::where('user_id', $teacher->id)
            ->where('term_id', $activeTerm->id)
            ->where('active', true)
            ->whereHas('role', function ($q) {
                $q->where('level', Role::LEVEL_TEACHER);
            })
            ->pluck('class_id')
            ->filter()
            ->toArray();

        $this->info("Teacher's Class IDs: " . implode(', ', $teacherClassIds));
        $this->newLine();

        if (empty($teacherClassIds)) {
            $this->error('Teacher has no assigned classes!');
            return 1;
        }

        // Find students in teacher's classes
        $studentsInClasses = User::whereHas('termUsers', function ($q) use ($activeTerm, $teacherClassIds) {
            $q->where('term_id', $activeTerm->id)
              ->where('active', true)
              ->whereIn('class_id', $teacherClassIds)
              ->whereHas('role', function ($roleQuery) {
                  $roleQuery->where('level', Role::LEVEL_STUDENT);
              });
        })->with(['role'])->get();

        $this->info("Students in Teacher's Classes:");
        foreach ($studentsInClasses as $student) {
            $this->line("  - {$student->name} (ID: {$student->id}, Role: {$student->role->name})");
        }
        $this->newLine();

        $this->info("Total students teacher should see: " . $studentsInClasses->count());

        // Test the actual query from UserResource
        $this->newLine();
        $this->info("=== Testing UserResource Query ===");

        // Login as teacher
        Auth::guard('moonshine')->login($teacher);

        // Simulate the UserResource resolveQuery method
        $query = User::query()->with(['role', 'creator', 'updater']);
        $user = auth('moonshine')->user();
        
        if ($user instanceof User && $user->isTeacher()) {
            $activeTerm = Term::getActiveTerm();
            if ($activeTerm) {
                $teacherClassIds = TermUser::where('user_id', $user->id)
                    ->where('term_id', $activeTerm->id)
                    ->where('active', true)
                    ->whereHas('role', function ($roleQuery) {
                        $roleQuery->where('level', Role::LEVEL_TEACHER);
                    })
                    ->pluck('class_id')
                    ->filter()
                    ->toArray();

                if (!empty($teacherClassIds)) {
                    $query->whereHas('termUsers', function ($termQuery) use ($activeTerm, $teacherClassIds) {
                        $termQuery->where('term_id', $activeTerm->id)
                          ->where('active', true)
                          ->whereIn('class_id', $teacherClassIds)
                          ->whereHas('role', function ($roleQuery) {
                              $roleQuery->where('level', Role::LEVEL_STUDENT);
                          });
                    });
                }
            }
        }

        $filteredUsers = $query->get();
        $this->info("UserResource filtered query result: " . $filteredUsers->count() . " users");
        
        foreach ($filteredUsers as $user) {
            $this->line("  - {$user->name} (Role: {$user->role->name})");
        }

        Auth::guard('moonshine')->logout();

        return 0;
    }
}
