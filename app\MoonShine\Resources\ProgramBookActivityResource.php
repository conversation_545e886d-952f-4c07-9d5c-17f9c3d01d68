<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ProgramBookActivity;
use App\Models\Program;
use App\Models\Book;
use App\Models\User;
use App\Models\BookActivityType;
use App\Models\ProgramTaskInstance;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Layout\Box;

// Import related resources
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\BookResource;
use App\MoonShine\Resources\UserResource;
use App\MoonShine\Resources\BookActivityTypeResource;
use App\MoonShine\Resources\ProgramTaskInstanceResource;

/**
 * @extends BaseResource<ProgramBookActivity>
 */
#[Icon('pencil-square')]
class ProgramBookActivityResource extends BaseResource
{
    protected string $model = ProgramBookActivity::class;

    protected array $with = ['program', 'book', 'user', 'activityType', 'taskInstance', 'reviewer', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_book_activities');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class)
                ->sortable(),
            
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name,
                resource: BookResource::class)
                ->sortable(),
            
            BelongsTo::make(__('admin.student'), 'user', 
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class)
                ->sortable(),
            
            BelongsTo::make(__('admin.activity_type'), 'activityType', 
                formatted: fn(BookActivityType $type) => $type->name,
                resource: BookActivityTypeResource::class)
                ->sortable(),
            
            Text::make(__('admin.category'), 'activityType.category_name')
                ->badge('purple'),
            
            Number::make(__('admin.word_count'), 'word_count')
                ->badge('blue'),
            
            Number::make(__('admin.points_earned'), 'points_earned')
                ->badge('green'),
            
            Switcher::make(__('admin.is_completed'), 'is_completed')
                ->sortable(),
            
            Switcher::make(__('admin.is_reviewed'), 'is_reviewed')
                ->sortable(),
            
            Date::make(__('admin.completed_at'), 'completed_at')
                ->withTime()
                ->format('d.m.Y H:i'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.programs'), 'program', 
                    formatted: fn(Program $program) => $program->name,
                    resource: ProgramResource::class)
                    ->required()
                    ->placeholder(__('admin.select_program')),
                
                BelongsTo::make(__('admin.books'), 'book', 
                    formatted: fn(Book $book) => $book->name,
                    resource: BookResource::class)
                    ->required()
                    ->placeholder(__('admin.select_book')),
                
                BelongsTo::make(__('admin.student'), 'user', 
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class)
                    ->required()
                    ->placeholder(__('admin.select_student')),
                
                BelongsTo::make(__('admin.activity_type'), 'activityType', 
                    formatted: fn(BookActivityType $type) => $type->name,
                    resource: BookActivityTypeResource::class)
                    ->required()
                    ->placeholder(__('admin.select_activity_type')),
                
                BelongsTo::make(__('admin.task_instance'), 'taskInstance', 
                    formatted: fn(ProgramTaskInstance $instance) => 
                        $instance->programTask->name . ' - ' . $instance->user->name,
                    resource: ProgramTaskInstanceResource::class)
                    ->nullable()
                    ->placeholder(__('admin.select_task_instance')),
                
                Textarea::make(__('admin.activity_content'), 'activity_content')
                    ->placeholder(__('admin.enter_activity_content')),
                
                Flex::make([
                    Number::make(__('admin.word_count'), 'word_count')
                        ->min(0)
                        ->placeholder(__('admin.enter_word_count')),
                    
                    Number::make(__('admin.points_earned'), 'points_earned')
                        ->min(0)
                        ->default(0)
                        ->placeholder(__('admin.enter_points_earned')),
                ]),
                
                Flex::make([
                    Switcher::make(__('admin.is_completed'), 'is_completed')
                        ->default(false),
                    
                    Date::make(__('admin.completed_at'), 'completed_at')
                        ->withTime()
                        ->format('d.m.Y H:i'),
                ]),
                
                BelongsTo::make(__('admin.reviewed_by'), 'reviewer', 
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class)
                    ->nullable()
                    ->placeholder(__('admin.select_reviewer')),
                
                Flex::make([
                    Date::make(__('admin.reviewed_at'), 'reviewed_at')
                        ->withTime()
                        ->format('d.m.Y H:i'),
                ]),
                
                Textarea::make(__('admin.feedback'), 'feedback')
                    ->placeholder(__('admin.enter_feedback')),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.program_name'), 'program.name'),
            Text::make(__('admin.book_name'), 'book.name'),
            Text::make(__('admin.student_name'), 'user.name'),
            Text::make(__('admin.activity_type'), 'activityType.name')
                ->badge('blue'),
            Text::make(__('admin.category'), 'activityType.category_name')
                ->badge('purple'),
            Text::make(__('admin.task_instance'), 'taskInstance.programTask.name')
                ->badge('yellow'),
            Textarea::make(__('admin.activity_content'), 'activity_content'),
            Number::make(__('admin.word_count'), 'word_count')
                ->badge('blue'),
            Number::make(__('admin.points_earned'), 'points_earned')
                ->badge('green'),
            Switcher::make(__('admin.is_completed'), 'is_completed')
                ->disabled(),
            Date::make(__('admin.completed_at'), 'completed_at')
                ->format('d.m.Y H:i'),
            Text::make(__('admin.reviewed_by'), 'reviewer.name')
                ->badge('orange'),
            Date::make(__('admin.reviewed_at'), 'reviewed_at')
                ->format('d.m.Y H:i'),
            Textarea::make(__('admin.feedback'), 'feedback'),
            ...parent::getCommonDetailFields(),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name,
                resource: BookResource::class),
            BelongsTo::make(__('admin.student'), 'user', 
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class),
            BelongsTo::make(__('admin.activity_type'), 'activityType', 
                formatted: fn(BookActivityType $type) => $type->name,
                resource: BookActivityTypeResource::class),
            Switcher::make(__('admin.is_completed'), 'is_completed'),
            Switcher::make(__('admin.is_reviewed'), 'is_reviewed'),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'book_id' => ['required', 'exists:books,id'],
            'user_id' => ['required', 'exists:users,id'],
            'activity_type_id' => ['required', 'exists:book_activity_types,id'],
            'program_task_instance_id' => ['nullable', 'exists:program_task_instances,id'],
            'activity_content' => ['nullable', 'string'],
            'word_count' => ['nullable', 'integer', 'min:0'],
            'points_earned' => ['nullable', 'integer', 'min:0'],
            'is_completed' => ['boolean'],
            'completed_at' => ['nullable', 'date'],
            'reviewed_by' => ['nullable', 'exists:users,id'],
            'reviewed_at' => ['nullable', 'date'],
            'feedback' => ['nullable', 'string'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['program.name', 'book.name', 'user.name', 'activityType.name', 'activity_content'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }
}
