<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TagValue extends Model
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'taggable_id',
        'taggable_type',
        'tag_id',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'taggable_id' => 'integer',
            'taggable_type' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Taggable type constants.
     */
    const TYPE_ORGANIZATION = 1;
    const TYPE_BOOKS = 2;
    const TYPE_USERS = 3;

    /**
     * Get all taggable types.
     */
    public static function getTaggableTypes(): array
    {
        return [
            self::TYPE_ORGANIZATION => 'Organization',
            self::TYPE_BOOKS => 'Books',
            self::TYPE_USERS => 'Users',
        ];
    }

    /**
     * Get taggable type name.
     */
    public function getTaggableTypeNameAttribute(): string
    {
        return self::getTaggableTypes()[$this->taggable_type] ?? 'Unknown';
    }

    /**
     * Get the tag.
     */
    public function tag(): BelongsTo
    {
        return $this->belongsTo(Tag::class);
    }

    /**
     * Get the taggable model (polymorphic-like).
     */
    public function getTaggableAttribute()
    {
        switch ($this->taggable_type) {
            case self::TYPE_ORGANIZATION:
                return Organization::find($this->taggable_id);
            case self::TYPE_BOOKS:
                return Book::find($this->taggable_id);
            case self::TYPE_USERS:
                return User::find($this->taggable_id);
            default:
                return null;
        }
    }

    /**
     * Scope to filter by taggable type.
     */
    public function scopeByTaggableType($query, int $type)
    {
        return $query->where('taggable_type', $type);
    }

    /**
     * Scope to filter by taggable model.
     */
    public function scopeByTaggable($query, $model)
    {
        $type = null;
        
        if ($model instanceof Organization) {
            $type = self::TYPE_ORGANIZATION;
        } elseif ($model instanceof Book) {
            $type = self::TYPE_BOOKS;
        } elseif ($model instanceof User) {
            $type = self::TYPE_USERS;
        }
        
        if ($type) {
            return $query->where('taggable_type', $type)
                        ->where('taggable_id', $model->id);
        }
        
        return $query->whereRaw('1 = 0'); // Return empty result
    }

    /**
     * Scope to get organization tag values.
     */
    public function scopeOrganizations($query)
    {
        return $query->where('taggable_type', self::TYPE_ORGANIZATION);
    }

    /**
     * Scope to get book tag values.
     */
    public function scopeBooks($query)
    {
        return $query->where('taggable_type', self::TYPE_BOOKS);
    }

    /**
     * Scope to get user tag values.
     */
    public function scopeUsers($query)
    {
        return $query->where('taggable_type', self::TYPE_USERS);
    }

    /**
     * Create a tag value for a model.
     */
    public static function createForModel($model, Tag $tag): ?TagValue
    {
        $type = null;
        
        if ($model instanceof Organization) {
            $type = self::TYPE_ORGANIZATION;
        } elseif ($model instanceof Book) {
            $type = self::TYPE_BOOKS;
        } elseif ($model instanceof User) {
            $type = self::TYPE_USERS;
        }
        
        if ($type && $tag->tag_type === $type) {
            return static::create([
                'taggable_id' => $model->id,
                'taggable_type' => $type,
                'tag_id' => $tag->id,
            ]);
        }
        
        return null;
    }
}
