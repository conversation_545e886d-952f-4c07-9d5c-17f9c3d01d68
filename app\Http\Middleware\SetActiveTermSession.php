<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Term;
use Symfony\Component\HttpFoundation\Response;

class SetActiveTermSession
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only set active term for authenticated users
        if (Auth::guard('moonshine')->check()) {
            $this->setActiveTermInSession($request);
        }

        return $next($request);
    }

    /**
     * Set the active term ID in session.
     */
    private function setActiveTermInSession(Request $request): void
    {
        // Check if active term is already set in session
        if (!$request->session()->has('active_term_id')) {
            $activeTerm = Term::getActiveTerm();
            
            if ($activeTerm) {
                $request->session()->put('active_term_id', $activeTerm->id);
                $request->session()->put('active_term_name', $activeTerm->name);
            }
        }
    }
}
