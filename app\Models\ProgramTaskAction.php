<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProgramTaskAction extends BaseModel
{
    use SoftDeletes;

    /**
     * Action type constants.
     */
    const TYPE_COMPLETED = 'completed';
    const TYPE_MISSED = 'missed';
    const TYPE_EXCUSED = 'excused';
    const TYPE_REASSIGNED = 'reassigned';

    /**
     * The table associated with the model.
     */
    protected $table = 'program_task_actions';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_task_instance_id',
        'action_type',
        'action_date',
        'notes',
        'points_awarded',
        'completed_by',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'action_date' => 'datetime',
            'points_awarded' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get all action types.
     */
    public static function getActionTypes(): array
    {
        return [
            self::TYPE_COMPLETED => 'Completed',
            self::TYPE_MISSED => 'Missed',
            self::TYPE_EXCUSED => 'Excused',
            self::TYPE_REASSIGNED => 'Reassigned',
        ];
    }

    /**
     * Get action type name.
     */
    public function getActionTypeNameAttribute(): string
    {
        return self::getActionTypes()[$this->action_type] ?? 'Unknown';
    }

    /**
     * Get the task instance this action belongs to.
     */
    public function programTaskInstance(): BelongsTo
    {
        return $this->belongsTo(ProgramTaskInstance::class);
    }

    /**
     * Get the user who completed/excused the task.
     */
    public function completedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'completed_by');
    }

    /**
     * Scope to filter by action type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('action_type', $type);
    }

    /**
     * Scope to get completed actions.
     */
    public function scopeCompleted($query)
    {
        return $query->where('action_type', self::TYPE_COMPLETED);
    }

    /**
     * Scope to get missed actions.
     */
    public function scopeMissed($query)
    {
        return $query->where('action_type', self::TYPE_MISSED);
    }

    /**
     * Scope to get excused actions.
     */
    public function scopeExcused($query)
    {
        return $query->where('action_type', self::TYPE_EXCUSED);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('action_date', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by user who completed.
     */
    public function scopeCompletedBy($query, int $userId)
    {
        return $query->where('completed_by', $userId);
    }

    /**
     * Scope to get actions with points awarded.
     */
    public function scopeWithPoints($query)
    {
        return $query->whereNotNull('points_awarded')
                    ->where('points_awarded', '>', 0);
    }

    /**
     * Check if action awarded points.
     */
    public function hasPointsAwarded(): bool
    {
        return !is_null($this->points_awarded) && $this->points_awarded > 0;
    }

    /**
     * Get formatted action date.
     */
    public function getFormattedActionDateAttribute(): string
    {
        return $this->action_date?->format('d.m.Y H:i') ?? '';
    }

    /**
     * Get action summary.
     */
    public function getActionSummaryAttribute(): string
    {
        $summary = $this->action_type_name;
        
        if ($this->hasPointsAwarded()) {
            $summary .= " (+{$this->points_awarded} points)";
        }
        
        if ($this->completedBy) {
            $summary .= " by {$this->completedBy->name}";
        }
        
        return $summary;
    }

    /**
     * Get action badge color based on type.
     */
    public function getActionBadgeColorAttribute(): string
    {
        return match($this->action_type) {
            self::TYPE_COMPLETED => 'green',
            self::TYPE_MISSED => 'red',
            self::TYPE_EXCUSED => 'yellow',
            self::TYPE_REASSIGNED => 'blue',
            default => 'gray',
        };
    }
}
