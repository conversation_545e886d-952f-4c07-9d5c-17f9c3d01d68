<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ProgramTeam;
use App\Models\ProgramTeamMember;
use App\Models\TermUser;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\ProgramTeamResource;
use App\MoonShine\Resources\TermUserResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;

/**
 * @extends BaseResource<ProgramTeamMember>
 */
class ProgramTeamMemberResource extends BaseResource
{
    protected string $model = ProgramTeamMember::class;

    protected array $with = ['programTeam', 'termUser', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_team_members');
    }



    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.program_teams'), 'programTeam', 
                formatted: fn(ProgramTeam $team) => $team->name)
                ->sortable(),
            Text::make(__('admin.programs'), 'programTeam.program.name')
                ->sortable(),
            BelongsTo::make(__('admin.term_users'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name)
                ->sortable(),
            Text::make(__('admin.roles'), 'termUser.role.name'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.program_teams'), 'programTeam', 
                    formatted: fn(ProgramTeam $team) => $team->name . ' (' . $team->program->name . ')',
                    resource: ProgramTeamResource::class)
                    ->required()
                    ->placeholder(__('admin.select_team'))
                    ->asyncSearch('name'),
                
                BelongsTo::make(__('admin.term_users'), 'termUser', 
                    formatted: fn(TermUser $termUser) => $termUser->user->name . ' (' . $termUser->role->name . ')',
                    resource: TermUserResource::class)
                    ->required()
                    ->placeholder(__('admin.select_student'))
                    ->asyncSearch('user.name'),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.program_teams'), 'programTeam', 
                formatted: fn(ProgramTeam $team) => $team->name),
            Text::make(__('admin.programs'), 'programTeam.program.name'),
            BelongsTo::make(__('admin.term_users'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name),
            Text::make(__('admin.roles'), 'termUser.role.name'),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.program_teams'), 'programTeam', 
                formatted: fn(ProgramTeam $team) => $team->name,
                resource: ProgramTeamResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_team_id' => ['required', 'exists:program_teams,id'],
            'term_user_id' => ['required', 'exists:term_users,id'],
            ...parent::getCommonRules($item),
        ];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }
}
