<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Term;
use App\Models\TermUser;
use App\Models\Organization;
use App\Models\SchoolClass;
use App\Models\GradeLevel;
use App\MoonShine\Resources\TeacherStudentsResource;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;

class TeacherStudentsResourceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        $this->systemAdminRole = Role::create(['name' => 'System Admin', 'level' => Role::LEVEL_SYSTEM_ADMIN, 'description' => 'System Administrator']);
        $this->schoolAdminRole = Role::create(['name' => 'School Admin', 'level' => Role::LEVEL_SCHOOL_ADMIN, 'description' => 'School Administrator']);
        $this->teacherRole = Role::create(['name' => 'Teacher', 'level' => Role::LEVEL_TEACHER, 'description' => 'Teacher']);
        $this->studentRole = Role::create(['name' => 'Student', 'level' => Role::LEVEL_STUDENT, 'description' => 'Student']);
        
        // Create organization and grade level
        $this->organization = Organization::create([
            'name' => 'Test School',
            'type' => 'school',
            'active' => true,
            'created_by' => 1,
            'updated_by' => 1,
        ]);
        
        $this->gradeLevel = GradeLevel::create([
            'name' => 'Grade 1',
            'level' => 1,
            'created_by' => 1,
            'updated_by' => 1,
        ]);
        
        // Create school class
        $this->schoolClass = SchoolClass::create([
            'name' => 'Class A',
            'organization_id' => $this->organization->id,
            'grade_level_id' => $this->gradeLevel->id,
            'created_by' => 1,
            'updated_by' => 1,
        ]);
        
        // Create active term
        $this->term = Term::create([
            'name' => 'Term 1',
            'start_date' => now()->subMonth(),
            'end_date' => now()->addMonth(),
            'active' => true,
            'created_by' => 1,
            'updated_by' => 1,
        ]);
        
        // Create teacher user
        $this->teacher = User::factory()->create(['role_id' => $this->teacherRole->id]);
        
        // Create students
        $this->student1 = User::factory()->create(['role_id' => $this->studentRole->id]);
        $this->student2 = User::factory()->create(['role_id' => $this->studentRole->id]);
        $this->student3 = User::factory()->create(['role_id' => $this->studentRole->id]); // Not in teacher's class
        
        // Assign teacher to class
        TermUser::create([
            'user_id' => $this->teacher->id,
            'role_id' => $this->teacherRole->id,
            'organization_id' => $this->organization->id,
            'class_id' => $this->schoolClass->id,
            'term_id' => $this->term->id,
            'active' => true,
            'created_by' => 1,
            'updated_by' => 1,
        ]);
        
        // Assign students to class (only student1 and student2)
        TermUser::create([
            'user_id' => $this->student1->id,
            'role_id' => $this->studentRole->id,
            'organization_id' => $this->organization->id,
            'class_id' => $this->schoolClass->id,
            'term_id' => $this->term->id,
            'active' => true,
            'created_by' => 1,
            'updated_by' => 1,
        ]);
        
        TermUser::create([
            'user_id' => $this->student2->id,
            'role_id' => $this->studentRole->id,
            'organization_id' => $this->organization->id,
            'class_id' => $this->schoolClass->id,
            'term_id' => $this->term->id,
            'active' => true,
            'created_by' => 1,
            'updated_by' => 1,
        ]);
        
        // student3 is not assigned to any class
    }

    public function test_teacher_can_only_see_their_students()
    {
        Auth::guard('moonshine')->login($this->teacher);
        
        $resource = new TeacherStudentsResource();
        $query = $this->invokePrivateMethod($resource, 'resolveQuery');
        
        $students = $query->get();
        
        // Teacher should only see student1 and student2 (their assigned students)
        $this->assertCount(2, $students);
        $this->assertTrue($students->contains('id', $this->student1->id));
        $this->assertTrue($students->contains('id', $this->student2->id));
        $this->assertFalse($students->contains('id', $this->student3->id));
    }

    public function test_non_teacher_gets_empty_result()
    {
        Auth::guard('moonshine')->login($this->student1);
        
        $resource = new TeacherStudentsResource();
        $query = $this->invokePrivateMethod($resource, 'resolveQuery');
        
        $students = $query->get();
        
        // Non-teacher should see no students
        $this->assertCount(0, $students);
    }

    public function test_teacher_without_class_assignment_gets_empty_result()
    {
        // Create a teacher without class assignment
        $teacherWithoutClass = User::factory()->create(['role_id' => $this->teacherRole->id]);
        
        Auth::guard('moonshine')->login($teacherWithoutClass);
        
        $resource = new TeacherStudentsResource();
        $query = $this->invokePrivateMethod($resource, 'resolveQuery');
        
        $students = $query->get();
        
        // Teacher without class assignment should see no students
        $this->assertCount(0, $students);
    }

    public function test_teacher_can_create_students()
    {
        Auth::guard('moonshine')->login($this->teacher);
        
        $resource = new TeacherStudentsResource();
        $canCreate = $this->invokePrivateMethod($resource, 'checkCreatePermission', [$this->teacher]);
        
        $this->assertTrue($canCreate);
    }

    public function test_teacher_can_update_students()
    {
        Auth::guard('moonshine')->login($this->teacher);
        
        $resource = new TeacherStudentsResource();
        $canUpdate = $this->invokePrivateMethod($resource, 'checkUpdatePermission', [$this->teacher]);
        
        $this->assertTrue($canUpdate);
    }

    public function test_teacher_cannot_delete_students()
    {
        Auth::guard('moonshine')->login($this->teacher);
        
        $resource = new TeacherStudentsResource();
        $canDelete = $this->invokePrivateMethod($resource, 'checkDeletePermission', [$this->teacher]);
        
        $this->assertFalse($canDelete);
    }

    public function test_student_cannot_perform_any_actions()
    {
        Auth::guard('moonshine')->login($this->student1);
        
        $resource = new TeacherStudentsResource();
        
        $canCreate = $this->invokePrivateMethod($resource, 'checkCreatePermission', [$this->student1]);
        $canUpdate = $this->invokePrivateMethod($resource, 'checkUpdatePermission', [$this->student1]);
        $canDelete = $this->invokePrivateMethod($resource, 'checkDeletePermission', [$this->student1]);
        
        $this->assertFalse($canCreate);
        $this->assertFalse($canUpdate);
        $this->assertFalse($canDelete);
    }

    private function invokePrivateMethod($object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        
        return $method->invokeArgs($object, $parameters);
    }
}
