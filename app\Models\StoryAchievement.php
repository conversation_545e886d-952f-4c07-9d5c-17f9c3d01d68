<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StoryAchievement extends BaseModel
{
    /**
     * Achievement type constants.
     */
    const TYPE_ITEM = 'item';
    const TYPE_BADGE = 'badge';
    const TYPE_REWARD = 'reward';
    const TYPE_TROPHY = 'trophy';
    const TYPE_COLLECTIBLE = 'collectible';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'story_id',
        'name',
        'description',
        'type',
        'image',
        'unlock_rule_id',
        'map_start_x',
        'map_start_y',
        'map_end_x',
        'map_end_y',
        'is_dynamic_position',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'map_start_x' => 'integer',
            'map_start_y' => 'integer',
            'map_end_x' => 'integer',
            'map_end_y' => 'integer',
            'is_dynamic_position' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get all achievement types.
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_ITEM => 'Item',
            self::TYPE_BADGE => 'Badge',
            self::TYPE_REWARD => 'Reward',
            self::TYPE_TROPHY => 'Trophy',
            self::TYPE_COLLECTIBLE => 'Collectible',
        ];
    }

    /**
     * Get achievement type display name.
     */
    public function getTypeDisplayNameAttribute(): string
    {
        return self::getTypes()[$this->type] ?? ucfirst($this->type);
    }

    /**
     * Get the story this achievement belongs to.
     */
    public function story(): BelongsTo
    {
        return $this->belongsTo(Story::class);
    }

    /**
     * Get the unlock rule for this achievement.
     */
    public function unlockRule(): BelongsTo
    {
        return $this->belongsTo(StoryRule::class, 'unlock_rule_id');
    }

    /**
     * Check if achievement has unlock rule.
     */
    public function hasUnlockRule(): bool
    {
        return !is_null($this->unlock_rule_id);
    }

    /**
     * Check if achievement has map coordinates.
     */
    public function hasMapCoordinates(): bool
    {
        return !is_null($this->map_start_x) && !is_null($this->map_start_y);
    }

    /**
     * Check if achievement has dynamic position.
     */
    public function hasDynamicPosition(): bool
    {
        return $this->is_dynamic_position;
    }

    /**
     * Get start coordinates as array.
     */
    public function getStartCoordinatesAttribute(): ?array
    {
        if (!$this->hasMapCoordinates()) {
            return null;
        }
        
        return [
            'x' => $this->map_start_x,
            'y' => $this->map_start_y,
        ];
    }

    /**
     * Get end coordinates as array.
     */
    public function getEndCoordinatesAttribute(): ?array
    {
        if (is_null($this->map_end_x) || is_null($this->map_end_y)) {
            return null;
        }
        
        return [
            'x' => $this->map_end_x,
            'y' => $this->map_end_y,
        ];
    }

    /**
     * Get all coordinates as array.
     */
    public function getCoordinatesAttribute(): array
    {
        return [
            'start' => $this->start_coordinates,
            'end' => $this->end_coordinates,
            'is_dynamic' => $this->is_dynamic_position,
        ];
    }

    /**
     * Get formatted achievement info.
     */
    public function getInfoAttribute(): string
    {
        $info = $this->type_display_name;
        
        if ($this->hasMapCoordinates()) {
            $info .= ' (Map: ' . $this->map_start_x . ',' . $this->map_start_y . ')';
        }
        
        if ($this->hasDynamicPosition()) {
            $info .= ' [Dynamic]';
        }
        
        return $info;
    }

    /**
     * Scope to filter by type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get achievements for a specific story.
     */
    public function scopeForStory($query, int $storyId)
    {
        return $query->where('story_id', $storyId);
    }

    /**
     * Scope to get achievements with map coordinates.
     */
    public function scopeWithMapCoordinates($query)
    {
        return $query->whereNotNull('map_start_x')
                    ->whereNotNull('map_start_y');
    }

    /**
     * Scope to get achievements with dynamic positions.
     */
    public function scopeDynamicPosition($query)
    {
        return $query->where('is_dynamic_position', true);
    }

    /**
     * Scope to get achievements with unlock rules.
     */
    public function scopeWithUnlockRules($query)
    {
        return $query->whereNotNull('unlock_rule_id');
    }

    /**
     * Scope to search achievements by name.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('description', 'like', '%' . $search . '%');
    }
}
