<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ProgramBookQuiz;
use App\Models\Program;
use App\Models\Book;
use App\Models\User;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Layout\Box;

// Import related resources
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\BookResource;
use App\MoonShine\Resources\UserResource;

/**
 * @extends BaseResource<ProgramBookQuiz>
 */
#[Icon('clipboard-document-check')]
class ProgramBookQuizResource extends BaseResource
{
    protected string $model = ProgramBookQuiz::class;

    protected array $with = ['program', 'book', 'user', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_book_quizzes');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name)
                ->sortable(),
            
            BelongsTo::make(__('admin.student'), 'user', 
                formatted: fn(User $user) => $user->name)
                ->sortable(),
            
            Select::make(__('admin.quiz_type'), 'quiz_type')
                ->options(ProgramBookQuiz::getQuizTypes())
                ->sortable(),
            
            Number::make(__('admin.score_percentage'), 'score_percentage')
                ->badge(fn($score) => $score >= 80 ? 'green' : ($score >= 60 ? 'yellow' : 'red')),
            
            Switcher::make(__('admin.is_passed'), 'is_passed')
                ->sortable(),
            
            Number::make(__('admin.attempt_number'), 'attempt_number')
                ->badge('blue'),
            
            Date::make(__('admin.started_at'), 'started_at')
                ->sortable()
                ->withTime()
                ->format('d.m.Y H:i'),
            
            Date::make(__('admin.completed_at'), 'completed_at')
                ->sortable()
                ->withTime()    
                ->format('d.m.Y H:i'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.programs'), 'program', 
                    formatted: fn(Program $program) => $program->name,
                    resource: ProgramResource::class)
                    ->required()
                    ->placeholder(__('admin.select_program')),
                
                BelongsTo::make(__('admin.books'), 'book', 
                    formatted: fn(Book $book) => $book->name,
                    resource: BookResource::class)
                    ->required()
                    ->placeholder(__('admin.select_book')),
                
                BelongsTo::make(__('admin.student'), 'user', 
                    formatted: fn(User $user) => $user->name,
                    resource: UserResource::class)
                    ->required()
                    ->placeholder(__('admin.select_student')),
                
                Flex::make([
                    Select::make(__('admin.quiz_type'), 'quiz_type')
                        ->options(ProgramBookQuiz::getQuizTypes())
                        ->required()
                        ->placeholder(__('admin.select_quiz_type')),
                    
                    Number::make(__('admin.attempt_number'), 'attempt_number')
                        ->required()
                        ->min(1)
                        ->default(1),
                ]),
                
                Flex::make([
                    Number::make(__('admin.total_questions'), 'total_questions')
                        ->required()
                        ->min(1)
                        ->placeholder(__('admin.enter_total_questions')),
                    
                    Number::make(__('admin.passing_score'), 'passing_score')
                        ->required()
                        ->min(0)
                        ->max(100)
                        ->default(60)
                        ->placeholder(__('admin.enter_passing_score')),
                ]),
                
                Flex::make([
                    Number::make(__('admin.time_limit_minutes'), 'time_limit_minutes')
                        ->min(1)
                        ->placeholder(__('admin.enter_time_limit')),
                    
                    Date::make(__('admin.started_at'), 'started_at')    
                        ->required()
                        ->default(now()),
                ]),
                
                Date::make(__('admin.completed_at'), 'completed_at'),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.program_name'), 'program.name'),
            Text::make(__('admin.book_name'), 'book.name'),
            Text::make(__('admin.student_name'), 'user.name'),
            Text::make(__('admin.quiz_type'), 'quiz_type_name')
                ->badge('blue'),
            Number::make(__('admin.total_questions'), 'total_questions')
                ->badge('gray'),
            Number::make(__('admin.correct_answers'), 'correct_answers')
                ->badge('green'),
            Number::make(__('admin.score_percentage'), 'score_percentage')
                ->badge(fn($score) => $score >= 80 ? 'green' : ($score >= 60 ? 'yellow' : 'red')),
            Number::make(__('admin.passing_score'), 'passing_score')
                ->badge('purple'),
            Switcher::make(__('admin.is_passed'), 'is_passed')
                ->disabled(),
            Number::make(__('admin.attempt_number'), 'attempt_number')
                ->badge('blue'),
            Number::make(__('admin.duration_minutes'), 'duration_minutes')
                ->badge('yellow'),
            Number::make(__('admin.time_limit_minutes'), 'time_limit_minutes')
                ->badge('orange'),
            Date::make(__('admin.started_at'), 'started_at')
                ->format('d.m.Y H:i'),
            Date::make(__('admin.completed_at'), 'completed_at')
                ->format('d.m.Y H:i'),
            ...parent::getCommonDetailFields(),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name,
                resource: BookResource::class),
            BelongsTo::make(__('admin.student'), 'user', 
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class),
            Select::make(__('admin.quiz_type'), 'quiz_type')
                ->options(ProgramBookQuiz::getQuizTypes()),
            Switcher::make(__('admin.is_passed'), 'is_passed'),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'book_id' => ['required', 'exists:books,id'],
            'user_id' => ['required', 'exists:users,id'],
            'quiz_type' => ['required', 'in:completion,daily_reading,practice'],
            'total_questions' => ['required', 'integer', 'min:1'],
            'passing_score' => ['required', 'numeric', 'min:0', 'max:100'],
            'attempt_number' => ['required', 'integer', 'min:1'],
            'started_at' => ['required', 'date'],
            'completed_at' => ['nullable', 'date', 'after:started_at'],
            'time_limit_minutes' => ['nullable', 'integer', 'min:1'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['program.name', 'book.name', 'user.name'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }
}
