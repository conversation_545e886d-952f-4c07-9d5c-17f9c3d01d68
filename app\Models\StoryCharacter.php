<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class StoryCharacter extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'story_id',
        'name',
        'description',
        'base_image',
        'active',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get the story this character belongs to.
     */
    public function story(): BelongsTo
    {
        return $this->belongsTo(Story::class);
    }

    /**
     * Get the stages for this character.
     */
    public function stages(): HasMany
    {
        return $this->hasMany(StoryCharacterStage::class, 'character_id')->orderBy('stage_number');
    }

    /**
     * Get the first stage for this character.
     */
    public function firstStage(): Has<PERSON><PERSON>
    {
        return $this->hasMany(StoryCharacterStage::class, 'character_id')->where('stage_number', 1);
    }

    /**
     * Get the total number of stages.
     */
    public function getStageCountAttribute(): int
    {
        return $this->stages()->count();
    }

    /**
     * Check if character has stages.
     */
    public function hasStages(): bool
    {
        return $this->stage_count > 0;
    }

    /**
     * Get the maximum stage number.
     */
    public function getMaxStageAttribute(): int
    {
        return $this->stages()->max('stage_number') ?? 0;
    }

    /**
     * Get stage by number.
     */
    public function getStage(int $stageNumber): ?StoryCharacterStage
    {
        return $this->stages()->where('stage_number', $stageNumber)->first();
    }

    /**
     * Get the base stage (stage 1).
     */
    public function getBaseStageAttribute(): ?StoryCharacterStage
    {
        return $this->getStage(1);
    }

    /**
     * Get the final stage (highest stage number).
     */
    public function getFinalStageAttribute(): ?StoryCharacterStage
    {
        return $this->stages()->orderBy('stage_number', 'desc')->first();
    }

    /**
     * Scope to get active characters.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to get characters for a specific story.
     */
    public function scopeForStory($query, int $storyId)
    {
        return $query->where('story_id', $storyId);
    }

    /**
     * Scope to search characters by name.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('description', 'like', '%' . $search . '%');
    }

    /**
     * Get character progression summary.
     */
    public function getProgressionSummaryAttribute(): string
    {
        if (!$this->hasStages()) {
            return 'No stages defined';
        }
        
        return $this->stage_count . ' stage' . ($this->stage_count > 1 ? 's' : '') . ' available';
    }

    /**
     * Check if character can be selected (is active and has at least one stage).
     */
    public function canBeSelected(): bool
    {
        return $this->active && $this->hasStages();
    }

    /**
     * Get the display image (base_image or first stage image).
     */
    public function getDisplayImageAttribute(): string
    {
        if ($this->hasStages() && $this->base_stage) {
            return $this->base_stage->image;
        }
        
        return $this->base_image;
    }
}
