<?php

use Illuminate\Support\Facades\Route;
use App\MoonShine\Pages\PrivacyAgreementPage;
use App\Http\Controllers\TeacherController;

Route::get('/', function () {
    return view('welcome');
});

// Privacy Agreement Routes
Route::middleware(['web'])->group(function () {
    Route::get('/admin/privacy-agreement', [PrivacyAgreementPage::class, 'handle'])
        ->name('moonshine.privacy-agreement');

    Route::post('/admin/process-privacy-consent', [PrivacyAgreementPage::class, 'processPrivacyConsent'])
        ->name('moonshine.process-privacy-consent');
});

// Teacher Interface Routes
Route::prefix('teacher')->name('teacher.')->middleware(['web'])->group(function () {
    // Public routes (no authentication required)
    Route::get('/login', [TeacherController::class, 'showLogin'])->name('login');
    Route::post('/login', [TeacherController::class, 'login']);
    Route::get('/offline', function () {
        return view('teacher.offline');
    })->name('offline');

    // Protected routes (require teacher authentication)
    Route::middleware(['App\Http\Middleware\TeacherAuthentication'])->group(function () {
        Route::get('/', [TeacherController::class, 'dashboard'])->name('dashboard');
        Route::get('/dashboard', [TeacherController::class, 'dashboard']);
        Route::post('/logout', [TeacherController::class, 'logout'])->name('logout');
    });
});

// Student Interface Routes
Route::prefix('student')->name('student.')->middleware(['web'])->group(function () {
    // Public routes (no authentication required)
    Route::get('/login', [App\Http\Controllers\StudentController::class, 'showLogin'])->name('login');
    Route::post('/login', [App\Http\Controllers\StudentController::class, 'login']);
    Route::get('/splash', [App\Http\Controllers\StudentController::class, 'splash'])->name('splash');
    Route::get('/offline', [App\Http\Controllers\StudentController::class, 'offline'])->name('offline');

    // Protected routes (require student authentication)
    Route::middleware(['App\Http\Middleware\StudentAuthentication'])->group(function () {
        Route::get('/', [App\Http\Controllers\StudentController::class, 'dashboard'])->name('dashboard');
        Route::get('/dashboard', [App\Http\Controllers\StudentController::class, 'dashboard']);
        Route::post('/logout', [App\Http\Controllers\StudentController::class, 'logout'])->name('logout');
    });
});

Route::get('/', function () {
    return view('welcome');
});
