<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StoryRuleDetail extends BaseModel
{
    /**
     * Required type constants.
     */
    const TYPE_ACHIEVEMENT = 1;
    const TYPE_BOOK = 2;
    const TYPE_CHAPTER = 3;
    const TYPE_CHARACTER_STAGE = 4;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'rule_id',
        'required_type',
        'required_id',
        'quantity',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'required_type' => 'integer',
            'required_id' => 'integer',
            'quantity' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get all required types.
     */
    public static function getRequiredTypes(): array
    {
        return [
            self::TYPE_ACHIEVEMENT => 'Achievement',
            self::TYPE_BOOK => 'Book',
            self::TYPE_CHAPTER => 'Chapter',
            self::TYPE_CHARACTER_STAGE => 'Character Stage',
        ];
    }

    /**
     * Get required type name.
     */
    public function getRequiredTypeNameAttribute(): string
    {
        return self::getRequiredTypes()[$this->required_type] ?? 'Unknown';
    }

    /**
     * Get the rule this detail belongs to.
     */
    public function rule(): BelongsTo
    {
        return $this->belongsTo(StoryRule::class, 'rule_id');
    }

    /**
     * Get the required item based on type.
     */
    public function getRequiredItemAttribute()
    {
        switch ($this->required_type) {
            case self::TYPE_ACHIEVEMENT:
                return StoryAchievement::find($this->required_id);
            case self::TYPE_BOOK:
                return Book::find($this->required_id);
            case self::TYPE_CHAPTER:
                return StoryChapter::find($this->required_id);
            case self::TYPE_CHARACTER_STAGE:
                return StoryCharacterStage::find($this->required_id);
            default:
                return null;
        }
    }

    /**
     * Get the name of the required item.
     */
    public function getRequiredItemNameAttribute(): string
    {
        $item = $this->required_item;
        
        if (!$item) {
            return 'Unknown Item';
        }
        
        return $item->name ?? $item->title ?? 'Unnamed Item';
    }

    /**
     * Get formatted description.
     */
    public function getDescriptionAttribute(): string
    {
        $description = $this->required_type_name . ': ' . $this->required_item_name;
        
        if ($this->quantity > 1) {
            $description .= ' (x' . $this->quantity . ')';
        }
        
        return $description;
    }

    /**
     * Scope to filter by required type.
     */
    public function scopeOfRequiredType($query, int $type)
    {
        return $query->where('required_type', $type);
    }

    /**
     * Scope to filter by required item.
     */
    public function scopeForRequiredItem($query, int $type, int $id)
    {
        return $query->where('required_type', $type)
                    ->where('required_id', $id);
    }
}
