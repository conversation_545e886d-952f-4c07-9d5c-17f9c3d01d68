<?php

namespace App\MoonShine\Resources;

use App\Models\StoryRuleDetail;
use App\Models\StoryRule;
use App\Models\Role;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\{Text, Number, Select};
use MoonShine\Laravel\Fields\Relationships\BelongsTo;

use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;

#[Icon('list-bullet')]
class StoryRuleDetailResource extends BaseResource
{
    protected string $model = StoryRuleDetail::class;

    protected string $column = 'description';

    protected array $with = ['rule', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.story_rule_details');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.rule'), 'rule', 
                formatted: fn(StoryRule $rule) => $rule->description,
                resource: StoryRuleResource::class)
                ->sortable(),
            
            Text::make(__('admin.required_type'), 'required_type_name')
                ->sortable(),
            
            Number::make(__('admin.required_id'), 'required_id')
                ->sortable(),
            
            Text::make(__('admin.required_item_name'), 'required_item_name'),
            
            Number::make(__('admin.quantity'), 'quantity')
                ->sortable(),
            
            Text::make(__('admin.description'), 'description'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        BelongsTo::make(__('admin.rule'), 'rule', 
                            formatted: fn(StoryRule $rule) => $rule->description,
                            resource: StoryRuleResource::class)
                            ->required()
                            ->placeholder(__('admin.select_rule')),
                        
                        Select::make(__('admin.required_type'), 'required_type')
                            ->required()
                            ->options(StoryRuleDetail::getRequiredTypes())
                            ->placeholder(__('admin.select_required_type')),
                        
                        Number::make(__('admin.required_id'), 'required_id')
                            ->required()
                            ->min(1)
                            ->placeholder('Enter the ID of the required item'),
                        
                        Number::make(__('admin.quantity'), 'quantity')
                            ->required()
                            ->min(1)
                            ->default(1)
                            ->placeholder(__('admin.enter_quantity')),
                    ]),
                    
                    Tab::make(__('admin.summary'), [
                        Text::make(__('admin.required_item_name'), 'required_item_name')
                            ->readonly(),
                        
                        Text::make(__('admin.description'), 'description')
                            ->readonly(),
                    ]),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(__('admin.rule'), 'rule', 
                formatted: fn(StoryRule $rule) => $rule->description,
                resource: StoryRuleResource::class),
            Text::make(__('admin.required_type'), 'required_type_name'),
            Number::make(__('admin.required_id'), 'required_id'),
            Text::make(__('admin.required_item_name'), 'required_item_name'),
            Number::make(__('admin.quantity'), 'quantity'),
            Text::make(__('admin.description'), 'description'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'rule_id' => ['required', 'exists:story_rules,id'],
            'required_type' => ['required', 'integer', 'min:1', 'max:4'],
            'required_id' => ['required', 'integer', 'min:1'],
            'quantity' => ['required', 'integer', 'min:1'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return [];
    }

    protected function getDefaultSort(): array
    {
        return ['id' => 'desc'];
    }
}
