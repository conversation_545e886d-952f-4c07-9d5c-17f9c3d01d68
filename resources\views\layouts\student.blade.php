<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', __('student.app_title'))</title>
    <meta name="description" content="@yield('description', __('student.app_description'))">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4F46E5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="{{ __('student.app_title') }}">
    
    <!-- PWA Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/student-manifest.json">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=nunito:400,500,600,700,800&display=swap" rel="stylesheet" />
    
    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- MoonShine Styles -->
    <link rel="stylesheet" href="{{ asset('vendor/moonshine/assets/main.css') }}">
    
    <style>
        /* Game-like color scheme */
        :root {
            --primary-blue: #4F46E5;
            --primary-green: #10B981;
            --primary-orange: #F59E0B;
            --primary-red: #EF4444;
            --primary-purple: #8B5CF6;
            --primary-pink: #EC4899;
            --secondary-blue: #DBEAFE;
            --secondary-green: #D1FAE5;
            --secondary-orange: #FEF3C7;
            --secondary-red: #FEE2E2;
            --secondary-purple: #EDE9FE;
            --secondary-pink: #FCE7F3;
            --game-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            --card-shadow-hover: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        /* Game-like font */
        body {
            font-family: 'Nunito', sans-serif;
            background: var(--game-bg);
            min-height: 100vh;
        }

        /* Game container */
        .game-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 1rem;
            min-height: 100vh;
        }
        
        @media (min-width: 768px) {
            .game-container {
                max-width: 480px;
                padding: 2rem;
            }
        }

        /* Game cards */
        .game-card {
            background: white;
            border-radius: 20px;
            box-shadow: var(--card-shadow);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 3px solid transparent;
            overflow: hidden;
        }

        .game-card:hover {
            box-shadow: var(--card-shadow-hover);
            transform: translateY(-2px);
        }

        .game-card.interactive {
            cursor: pointer;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        .game-card.interactive:active {
            transform: translateY(0px) scale(0.98);
        }

        /* Game buttons */
        .game-btn {
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
            color: white;
            border: none;
            border-radius: 25px;
            padding: 16px 32px;
            font-weight: 700;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
            min-height: 56px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .game-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(79, 70, 229, 0.4);
        }

        .game-btn:active {
            transform: translateY(0px) scale(0.98);
        }

        .game-btn.green {
            background: linear-gradient(135deg, var(--primary-green), #059669);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
        }

        .game-btn.orange {
            background: linear-gradient(135deg, var(--primary-orange), #D97706);
            box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
        }

        .game-btn.red {
            background: linear-gradient(135deg, var(--primary-red), #DC2626);
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
        }

        /* Progress bars */
        .progress-bar {
            background: #E5E7EB;
            border-radius: 20px;
            overflow: hidden;
            height: 12px;
            position: relative;
        }

        .progress-fill {
            background: linear-gradient(90deg, var(--primary-green), var(--primary-blue));
            height: 100%;
            border-radius: 20px;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Achievement badges */
        .achievement-badge {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 800;
            color: white;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .achievement-badge::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: sparkle 3s infinite;
        }

        @keyframes sparkle {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }

        /* Game stats grid */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        /* Mobile navigation */
        .mobile-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 3px solid #E5E7EB;
            padding: 12px 0;
            z-index: 50;
            box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: #6B7280;
            transition: all 0.3s ease;
            border-radius: 12px;
            margin: 0 4px;
        }

        .nav-item.active {
            color: var(--primary-blue);
            background: var(--secondary-blue);
        }

        .nav-item:hover {
            color: var(--primary-blue);
            transform: translateY(-2px);
        }

        /* Loading animations */
        .loading-dots {
            display: inline-flex;
            gap: 4px;
        }

        .loading-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            animation: bounce 1.4s infinite ease-in-out both;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes bounce {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        /* Toast notifications */
        .toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(-100px);
            background: white;
            color: #1F2937;
            padding: 16px 24px;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid var(--primary-green);
        }

        .toast.show {
            opacity: 1;
            transform: translateX(-50%) translateY(0px);
        }

        .toast.error {
            border-color: var(--primary-red);
        }

        /* Safe area support */
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }

        /* Splash screen animations */
        .splash-logo {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Game-specific utilities */
        .text-game-blue { color: var(--primary-blue); }
        .text-game-green { color: var(--primary-green); }
        .text-game-orange { color: var(--primary-orange); }
        .text-game-red { color: var(--primary-red); }
        .text-game-purple { color: var(--primary-purple); }

        .bg-game-blue { background: var(--primary-blue); }
        .bg-game-green { background: var(--primary-green); }
        .bg-game-orange { background: var(--primary-orange); }
        .bg-game-red { background: var(--primary-red); }
        .bg-game-purple { background: var(--primary-purple); }
    </style>
    
    @stack('styles')
</head>
<body class="antialiased">
    <!-- Loading overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
        <div class="game-card p-8 text-center">
            <div class="loading-dots text-game-blue mb-4">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
            </div>
            <p class="text-gray-600 font-semibold">{{ __('student.loading') }}</p>
        </div>
    </div>
    
    <!-- Toast notification -->
    <div id="toast" class="toast"></div>
    
    <!-- Main content -->
    <main class="min-h-screen pb-24" id="main-content">
        @yield('content')
    </main>
    
    <!-- Mobile navigation (only show on authenticated pages) -->
    @auth('moonshine')
        <nav class="mobile-nav safe-area-bottom">
            <div class="flex justify-around items-center px-4">
                <a href="{{ route('student.dashboard') }}" class="nav-item">
                    <div class="achievement-badge bg-game-blue w-8 h-8 text-sm">
                        🏠
                    </div>
                    <span class="text-xs font-semibold mt-1">{{ __('student.home') }}</span>
                </a>
                
                <a href="#" class="nav-item">
                    <div class="achievement-badge bg-game-green w-8 h-8 text-sm">
                        📚
                    </div>
                    <span class="text-xs font-semibold mt-1">{{ __('student.lessons') }}</span>
                </a>
                
                <a href="#" class="nav-item">
                    <div class="achievement-badge bg-game-orange w-8 h-8 text-sm">
                        🏆
                    </div>
                    <span class="text-xs font-semibold mt-1">{{ __('student.achievements') }}</span>
                </a>
                
                <a href="#" class="nav-item">
                    <div class="achievement-badge bg-game-purple w-8 h-8 text-sm">
                        👤
                    </div>
                    <span class="text-xs font-semibold mt-1">{{ __('student.profile') }}</span>
                </a>
            </div>
        </nav>
    @endauth

    <!-- Scripts -->
    <script>
        // Show/hide loading overlay
        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
            document.getElementById('loading-overlay').classList.add('flex');
        }

        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
            document.getElementById('loading-overlay').classList.remove('flex');
        }

        // Game-like toast notification
        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = 'toast show';

            if (type === 'error') {
                toast.classList.add('error');
            } else {
                toast.classList.remove('error');
            }

            // Auto hide after 3 seconds
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        // Haptic feedback simulation
        function hapticFeedback(type = 'light') {
            if (navigator.vibrate) {
                switch(type) {
                    case 'light':
                        navigator.vibrate(10);
                        break;
                    case 'medium':
                        navigator.vibrate(20);
                        break;
                    case 'heavy':
                        navigator.vibrate([30, 10, 30]);
                        break;
                    case 'success':
                        navigator.vibrate([50, 25, 50]);
                        break;
                    case 'error':
                        navigator.vibrate([100, 50, 100, 50, 100]);
                        break;
                }
            }
        }

        // Game button interactions
        function initGameInteractions() {
            // Add touch feedback to game buttons
            const gameButtons = document.querySelectorAll('.game-btn, .game-card.interactive, .nav-item');
            gameButtons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    hapticFeedback('light');
                });

                button.addEventListener('click', function() {
                    hapticFeedback('medium');
                });
            });

            // Add special effects to achievement badges
            const badges = document.querySelectorAll('.achievement-badge');
            badges.forEach(badge => {
                badge.addEventListener('click', function() {
                    hapticFeedback('success');
                    this.style.animation = 'none';
                    setTimeout(() => {
                        this.style.animation = 'sparkle 3s infinite';
                    }, 10);
                });
            });
        }

        // Progress bar animation
        function animateProgress(element, targetWidth, duration = 1000) {
            const startWidth = 0;
            const startTime = performance.now();

            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const currentWidth = startWidth + (targetWidth - startWidth) * progress;

                element.style.width = currentWidth + '%';

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    hapticFeedback('success');
                }
            }

            requestAnimationFrame(animate);
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initGameInteractions();

            // Handle form submissions with loading states
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    showLoading();
                });
            });

            // Animate progress bars on page load
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const targetWidth = bar.dataset.width || 0;
                setTimeout(() => {
                    animateProgress(bar, targetWidth);
                }, 500);
            });
        });

        // Service Worker registration for PWA
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('student-sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        // FCM integration preparation
        function initializeFCM() {
            // This will be implemented when Firebase is integrated
            console.log('FCM initialization placeholder');
        }

        // Game sound effects (placeholder)
        function playSound(type) {
            // Placeholder for game sound effects
            // Can be implemented with Web Audio API or HTML5 audio
            console.log('Playing sound:', type);
        }

        // Achievement unlock animation
        function unlockAchievement(title, description) {
            showToast(`🎉 ${title}: ${description}`, 'success');
            hapticFeedback('success');
            playSound('achievement');
        }

        // Level up animation
        function levelUp(newLevel) {
            showToast(`🎊 Level Up! You're now level ${newLevel}!`, 'success');
            hapticFeedback('heavy');
            playSound('levelup');
        }
    </script>

    @stack('scripts')
</body>
</html>
