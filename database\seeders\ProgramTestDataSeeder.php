<?php

namespace Database\Seeders;

use App\Models\Program;
use App\Models\Story;
use App\Models\User;
use Illuminate\Database\Seeder;

class ProgramTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user as creator
        $user = User::first();
        
        if (!$user) {
            $this->command->error('No users found. Please create a user first.');
            return;
        }

        // Get the first story
        $story = Story::first();
        
        if (!$story) {
            $this->command->error('No stories found. Please create a story first.');
            return;
        }

        // Create sample programs
        $programs = [
            [
                'name' => 'Spring Reading Challenge 2024',
                'description' => 'A comprehensive reading program for spring semester focusing on adventure stories and character development.',
                'story_id' => $story->id,
                'start_date' => now()->addDays(7),
                'end_date' => now()->addDays(90),
                'is_active' => true,
                'created_by' => $user->id,
            ],
            [
                'name' => 'Summer Reading Adventure',
                'description' => 'An exciting summer reading program with gamification elements to keep students engaged during vacation.',
                'story_id' => $story->id,
                'start_date' => now()->addDays(100),
                'end_date' => now()->addDays(160),
                'is_active' => true,
                'created_by' => $user->id,
            ],
            [
                'name' => 'Fall Literacy Program',
                'description' => 'A structured fall program focusing on reading comprehension and vocabulary building.',
                'story_id' => $story->id,
                'start_date' => now()->subDays(30),
                'end_date' => now()->addDays(30),
                'is_active' => true,
                'created_by' => $user->id,
            ],
            [
                'name' => 'Winter Reading Club',
                'description' => 'A cozy winter reading program with indoor activities and storytelling sessions.',
                'story_id' => $story->id,
                'start_date' => now()->addDays(200),
                'end_date' => now()->addDays(280),
                'is_active' => false,
                'created_by' => $user->id,
            ],
        ];

        foreach ($programs as $programData) {
            Program::create($programData);
            $this->command->info("Created program: {$programData['name']}");
        }

        $this->command->info('Program test data seeded successfully!');
    }
}
