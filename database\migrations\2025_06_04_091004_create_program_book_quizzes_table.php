<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('program_book_quizzes', function (Blueprint $table) {
            $table->id();
            
            // Core quiz information
            $table->foreignId('program_id')->constrained('programs')->onDelete('cascade');
            $table->foreignId('book_id')->constrained('books')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')
                  ->comment('The student taking the quiz');
            
            // Quiz configuration
            $table->enum('quiz_type', ['completion', 'daily_reading', 'practice'])
                  ->comment('Type of quiz');
            $table->integer('total_questions')->comment('Number of questions in this quiz');
            $table->integer('correct_answers')->default(0)->comment('Number of correct answers');
            $table->decimal('score_percentage', 5, 2)->default(0.00)
                  ->comment('Score as percentage (0.00-100.00)');
            $table->decimal('passing_score', 5, 2)->comment('Required score to pass');
            $table->boolean('is_passed')->default(false)->comment('Whether student passed');
            $table->integer('attempt_number')->default(1)->comment('Which attempt this is');
            
            // Timing information
            $table->datetime('started_at')->comment('When quiz was started');
            $table->datetime('completed_at')->nullable()->comment('When quiz was completed');
            $table->integer('time_limit_minutes')->nullable()->comment('Time limit for quiz');
            
            // Audit fields
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Indexes for performance
            $table->index(['program_id', 'book_id', 'user_id']);
            $table->index(['user_id', 'quiz_type']);
            $table->index(['book_id', 'quiz_type']);
            $table->index(['is_passed', 'quiz_type']);
            $table->index(['started_at', 'completed_at']);
            $table->index('attempt_number');
            
            // Composite index for student quiz history
            $table->index(['user_id', 'book_id', 'attempt_number'], 'user_book_attempt_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('program_book_quizzes');
    }
};
