<?php

namespace App\MoonShine\Resources;

use App\Models\StoryCharacter;
use App\Models\Story;
use App\Models\Role;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\{Text, Textarea, Image, Switcher};
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;

#[Icon('user')]
class StoryCharacterResource extends BaseResource
{
    protected string $model = StoryCharacter::class;

    protected string $column = 'name';

    protected array $with = ['story', 'stages', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.story_characters');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.story'), 'story', 
                formatted: fn(Story $story) => $story->title,
                resource: StoryResource::class)
                ->sortable(),
            
            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Image::make(__('admin.base_image'), 'base_image'),
            
            Text::make(__('admin.stage_count'), 'stage_count'),
            
            Text::make(__('admin.progression_summary'), 'progression_summary'),
            
            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        BelongsTo::make(__('admin.story'), 'story', 
                            formatted: fn(Story $story) => $story->title,
                            resource: StoryResource::class)
                            ->required()
                            ->placeholder(__('admin.select_story')),
                        
                        Text::make(__('admin.name'), 'name')
                            ->required()
                            ->placeholder(__('admin.enter_name')),
                        
                        Textarea::make(__('admin.description'), 'description')
                            ->required()
                            ->placeholder(__('admin.enter_description')),
                        
                        Image::make(__('admin.base_image'), 'base_image')
                            ->required(),
                        
                        Switcher::make(__('admin.active'), 'active')
                            ->default(true),
                    ]),
                    
                    Tab::make(__('admin.summary'), [
                        Text::make(__('admin.stage_count'), 'stage_count')
                            ->readonly(),
                        
                        Text::make(__('admin.progression_summary'), 'progression_summary')
                            ->readonly(),
                    ]),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(__('admin.story'), 'story', 
                formatted: fn(Story $story) => $story->title,
                resource: StoryResource::class),
            Text::make(__('admin.name'), 'name'),
            Textarea::make(__('admin.description'), 'description'),
            Image::make(__('admin.base_image'), 'base_image'),
            Switcher::make(__('admin.active'), 'active'),
            Text::make(__('admin.stage_count'), 'stage_count'),
            Text::make(__('admin.progression_summary'), 'progression_summary'),
            HasMany::make(__('admin.story_character_stages'), 'stages', 
                resource: StoryCharacterStageResource::class),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'story_id' => ['required', 'exists:stories,id'],
            'name' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'base_image' => ['required', 'string', 'max:255'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['name', 'description'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
