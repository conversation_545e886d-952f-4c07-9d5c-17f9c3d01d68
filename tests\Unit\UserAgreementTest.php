<?php

namespace Tests\Unit;

use App\Models\UserAgreement;
use PHPUnit\Framework\TestCase;

class UserAgreementTest extends TestCase
{
    public function test_agreement_type_constants_exist()
    {
        // Test that the constants are defined
        $this->assertTrue(defined('App\Models\UserAgreement::TYPE_PRIVACY_POLICY'));
        $this->assertTrue(defined('App\Models\UserAgreement::TYPE_TERMS_OF_SERVICE'));
        $this->assertTrue(defined('App\Models\UserAgreement::TYPE_COOKIE_POLICY'));
    }

    public function test_agreement_constants_are_defined()
    {
        $this->assertEquals('privacy_policy', UserAgreement::TYPE_PRIVACY_POLICY);
        $this->assertEquals('terms_of_service', UserAgreement::TYPE_TERMS_OF_SERVICE);
        $this->assertEquals('cookie_policy', UserAgreement::TYPE_COOKIE_POLICY);
        $this->assertEquals('1.0', UserAgreement::CURRENT_PRIVACY_VERSION);
        $this->assertEquals('1.0', UserAgreement::CURRENT_TERMS_VERSION);
    }

    public function test_user_agreement_fillable_fields_are_correct()
    {
        $userAgreement = new UserAgreement();
        $expectedFillable = [
            'user_id',
            'agreement_type',
            'version',
            'accepted_at',
            'ip_address',
            'created_by',
            'updated_by',
        ];
        
        $this->assertEquals($expectedFillable, $userAgreement->getFillable());
    }
}
