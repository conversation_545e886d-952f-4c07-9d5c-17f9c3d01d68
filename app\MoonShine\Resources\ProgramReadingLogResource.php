<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ProgramReadingLog;
use App\Models\Program;
use App\Models\Book;
use App\Models\User;
use App\Models\ProgramTaskInstance;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Support\Enums\ClickAction;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;

// Import related resources
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\BookResource;
use App\MoonShine\Resources\UserResource;
use App\MoonShine\Resources\ProgramTaskInstanceResource;

/**
 * @extends BaseResource<ProgramReadingLog>
 */
#[Icon('book-open')]
class ProgramReadingLogResource extends BaseResource
{
    protected string $model = ProgramReadingLog::class;

    protected array $with = ['program', 'book', 'user', 'taskInstance', 'verifier', 'creator', 'updater'];

    protected int $itemsPerPage = 10;

    protected ?ClickAction $clickAction = ClickAction::DETAIL;

    public function getTitle(): string
    {
        return __('admin.program_reading_logs');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.programs'),
                'program',
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.books'),
                'book',
                formatted: fn(Book $book) => $book->name,
                resource: BookResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.student'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->sortable(),

            Date::make(__('admin.reading_date'), 'reading_date')
                ->sortable(),
            /*
            Text::make(__('admin.page_range'), function($log) {
                return "Pages {$log->start_page}-{$log->end_page}";
            })->badge('blue'),
            */
            Number::make(__('admin.pages_read'), 'pages_read')
                ->badge('green'),

            Number::make(__('admin.reading_duration_minutes'), 'reading_duration_minutes')
                ->badge('yellow'),

            Number::make(__('admin.reading_speed'), 'reading_speed_pages_per_minute')
                ->badge('purple'),

            Number::make(__('admin.points_awarded'), 'points_awarded')
                ->badge('orange'),

            Switcher::make(__('admin.is_verified'), 'is_verified')
                ->sortable(),

            BelongsTo::make(
                __('admin.verified_by'),
                'verifier',
                formatted: fn(?User $user) => $user?->name ?? '-',
                resource: UserResource::class
            )
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        BelongsTo::make(
                            __('admin.programs'),
                            'program',
                            formatted: fn(Program $program) => $program->name,
                            resource: ProgramResource::class
                        )
                            ->required()
                            ->placeholder(__('admin.select_program')),

                        BelongsTo::make(
                            __('admin.books'),
                            'book',
                            formatted: fn(Book $book) => $book->name,
                            resource: BookResource::class
                        )
                            ->required()
                            ->placeholder(__('admin.select_book')),

                        BelongsTo::make(
                            __('admin.student'),
                            'user',
                            formatted: fn(User $user) => $user->name,
                            resource: UserResource::class
                        )
                            ->required()
                            ->placeholder(__('admin.select_student')),

                        Date::make(__('admin.reading_date'), 'reading_date')
                            ->required()
                            ->default(now()->toDateString())
                            ->placeholder(__('admin.select_reading_date')),
                    ]),

                    Tab::make(__('admin.reading_details'), [
                        Flex::make([
                            Number::make(__('admin.start_page'), 'start_page')
                                ->required()
                                ->min(1)
                                ->placeholder(__('admin.enter_start_page')),

                            Number::make(__('admin.end_page'), 'end_page')
                                ->required()
                                ->min(1)
                                ->placeholder(__('admin.enter_end_page')),
                        ]),

                        Number::make(__('admin.reading_duration_minutes'), 'reading_duration_minutes')
                            ->min(1)
                            ->placeholder(__('admin.enter_reading_duration')),

                        Textarea::make(__('admin.reading_notes'), 'reading_notes')
                            ->placeholder(__('admin.enter_reading_notes')),
                    ]),

                    Tab::make(__('admin.task_integration'), [
                        BelongsTo::make(
                            __('admin.task_instance'),
                            'taskInstance',
                            formatted: fn(?ProgramTaskInstance $instance) =>
                            $instance ? $instance->programTask->name . ' - ' . $instance->user->name : null,
                            resource: ProgramTaskInstanceResource::class
                        )
                            ->nullable()
                            ->placeholder(__('admin.select_task_instance')),
                    ]),

                    Tab::make(__('admin.verification'), [
                        Switcher::make(__('admin.is_verified'), 'is_verified')
                            ->default(false),

                        BelongsTo::make(
                            __('admin.verified_by'),
                            'verifier',
                            formatted: fn(?User $user) => $user?->name,
                            resource: UserResource::class
                        )
                            ->nullable()
                            ->placeholder(__('admin.select_verifier')),

                        Date::make(__('admin.verified_at'), 'verified_at')
                            ->withTime()
                            ->format('d.m.Y H:i'),

                        Number::make(__('admin.points_awarded'), 'points_awarded')
                            ->min(0)
                            ->default(0)
                            ->placeholder(__('admin.enter_points_awarded')),
                    ]),
                ]),
            ]),

            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.program_name'), 'program.name'),
            Text::make(__('admin.book_name'), 'book.name'),
            Text::make(__('admin.student_name'), 'user.name'),
            Date::make(__('admin.reading_date'), 'reading_date')
                ->format('d.m.Y'),
            Number::make(__('admin.start_page'), 'start_page')
                ->badge('blue'),
            Number::make(__('admin.end_page'), 'end_page')
                ->badge('blue'),
            Number::make(__('admin.pages_read'), 'pages_read')
                ->badge('green'),
            Number::make(__('admin.reading_duration_minutes'), 'reading_duration_minutes')
                ->badge('yellow'),
            Number::make(__('admin.reading_speed'), 'reading_speed_pages_per_minute')
                ->badge('purple'),
            Textarea::make(__('admin.reading_notes'), 'reading_notes'),
            Text::make(__('admin.task_instance'), 'taskInstance.programTask.name')
                ->badge('orange'),
            Switcher::make(__('admin.is_verified'), 'is_verified')
                ->disabled(),
            Text::make(__('admin.verified_by'), 'verifier.name')
                ->badge('gray'),
            Date::make(__('admin.verified_at'), 'verified_at')
                ->format('d.m.Y H:i'),
            Number::make(__('admin.points_awarded'), 'points_awarded')
                ->badge('green'),
            ...parent::getCommonDetailFields(),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.programs'),
                'program',
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class
            ),
            BelongsTo::make(
                __('admin.books'),
                'book',
                formatted: fn(Book $book) => $book->name,
                resource: BookResource::class
            ),
            BelongsTo::make(
                __('admin.student'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            ),
            Date::make(__('admin.reading_date'), 'reading_date'),
            Switcher::make(__('admin.is_verified'), 'is_verified'),
            BelongsTo::make(
                __('admin.verified_by'),
                'verifier',
                formatted: fn(?User $user) => $user?->name,
                resource: UserResource::class
            ),
        ];
    }

    public function rules(mixed $item): array
    {
        $programId = $item->program_id ?? request('program_id');
        $bookId = $item->book_id ?? request('book_id');
        $userId = $item->user_id ?? request('user_id');
        $readingDate = $item->reading_date ?? request('reading_date');

        return [
            'program_id' => ['required', 'exists:programs,id'],
            'book_id' => ['required', 'exists:books,id'],
            'user_id' => ['required', 'exists:users,id'],
            'program_task_instance_id' => ['nullable', 'exists:program_task_instances,id'],
            'reading_date' => [
                'required',
                'date',
                'before_or_equal:today',
                // Unique constraint validation
                function ($attribute, $value, $fail) use ($item, $programId, $bookId, $userId) {
                    if ($programId && $bookId && $userId && $value) {
                        $query = ProgramReadingLog::where('program_id', $programId)
                            ->where('book_id', $bookId)
                            ->where('user_id', $userId)
                            ->where('reading_date', $value);

                        if ($item && $item->exists) {
                            $query->where('id', '!=', $item->id);
                        }

                        if ($query->exists()) {
                            $fail(__('admin.reading_log_already_exists_for_date'));
                        }
                    }
                }
            ],
            'start_page' => ['required', 'integer', 'min:1'],
            'end_page' => ['required', 'integer', 'min:1', 'gte:start_page'],
            'reading_duration_minutes' => ['nullable', 'integer', 'min:1'],
            'reading_notes' => ['nullable', 'string'],
            'is_verified' => ['boolean'],
            'verified_by' => ['nullable', 'exists:users,id'],
            'verified_at' => ['nullable', 'date'],
            'points_awarded' => ['nullable', 'integer', 'min:0'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['user.name', 'book.name', 'program.name', 'reading_notes'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

    protected function getItemsPerPage(): int
    {
        $default = $this->itemsPerPage;

        $value = (int)(session()?->get('perPage') ?? $default);
        /*
        if (! in_array($value, $this->perPageValues())) {
            return $default;
        }
*/
        return $value;
    }
}
