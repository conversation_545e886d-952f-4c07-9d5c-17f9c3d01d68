<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Term extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'start_date',
        'end_date',
        'active',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'start_date' => 'date',
            'end_date' => 'date',
            'active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Ensure only one active term at a time
        static::saving(function ($term) {
            if ($term->active) {
                // Deactivate all other terms
                static::where('id', '!=', $term->id)->update(['active' => false]);
            }
        });
    }

    /**
     * Get term users for this term.
     */
    public function termUsers(): Has<PERSON>any
    {
        return $this->hasMany(TermUser::class);
    }

    /**
     * Get active term users for this term.
     */
    public function activeTermUsers(): HasMany
    {
        return $this->hasMany(TermUser::class)->where('active', true);
    }

    /**
     * Scope to get active term.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to get current terms (based on date).
     */
    public function scopeCurrent($query)
    {
        $now = Carbon::now()->toDateString();
        return $query->where('start_date', '<=', $now)
                    ->where('end_date', '>=', $now);
    }

    /**
     * Scope to get future terms.
     */
    public function scopeFuture($query)
    {
        return $query->where('start_date', '>', Carbon::now()->toDateString());
    }

    /**
     * Scope to get past terms.
     */
    public function scopePast($query)
    {
        return $query->where('end_date', '<', Carbon::now()->toDateString());
    }

    /**
     * Get the active term.
     */
    public static function getActiveTerm(): ?Term
    {
        return static::active()->first();
    }

    /**
     * Check if this term is currently active.
     */
    public function isActive(): bool
    {
        return $this->active;
    }

    /**
     * Check if this term is current (based on dates).
     */
    public function isCurrent(): bool
    {
        $now = Carbon::now()->toDateString();
        return $this->start_date <= $now && $this->end_date >= $now;
    }

    /**
     * Check if this term is in the future.
     */
    public function isFuture(): bool
    {
        return $this->start_date > Carbon::now()->toDateString();
    }

    /**
     * Check if this term is in the past.
     */
    public function isPast(): bool
    {
        return $this->end_date < Carbon::now()->toDateString();
    }

    /**
     * Get term duration in days.
     */
    public function getDurationAttribute(): int
    {
        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    /**
     * Get formatted date range.
     */
    public function getDateRangeAttribute(): string
    {
        return $this->start_date->format('d.m.Y') . ' - ' . $this->end_date->format('d.m.Y');
    }

    /**
     * Get term status.
     */
    public function getStatusAttribute(): string
    {
        if ($this->isFuture()) {
            return 'Future';
        } elseif ($this->isPast()) {
            return 'Past';
        } else {
            return 'Current';
        }
    }

    /**
     * Activate this term (deactivates others).
     */
    public function activate(): bool
    {
        return $this->update(['active' => true]);
    }

    /**
     * Deactivate this term.
     */
    public function deactivate(): bool
    {
        return $this->update(['active' => false]);
    }
}
