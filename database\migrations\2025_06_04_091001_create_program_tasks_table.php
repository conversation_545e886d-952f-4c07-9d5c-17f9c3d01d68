<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('program_tasks', function (Blueprint $table) {
            $table->id();
            
            // Core task information
            $table->foreignId('program_id')->constrained('programs')->onDelete('cascade');
            $table->string('name')->comment('Task title');
            $table->text('description')->nullable()->comment('Detailed task instructions');
            
            // Task configuration
            $table->enum('task_type', ['reading_log', 'activity', 'question', 'physical'])
                  ->comment('Type of task');
            $table->boolean('is_recurring')->default(false)
                  ->comment('One-time vs recurring task');
            $table->enum('recurrence_pattern', ['daily', 'weekly'])->nullable()
                  ->comment('For recurring tasks only');
            
            // Date management
            $table->date('start_date')->comment('When task becomes available');
            $table->date('end_date')->comment('Final deadline for task completion');
            
            // Optional configurations
            $table->integer('points')->nullable()->comment('Optional points awarded for completion');
            $table->foreignId('book_id')->nullable()->constrained('books')->onDelete('set null')
                  ->comment('Optional book reference');
            $table->integer('page_start')->nullable()->comment('For reading tasks: starting page');
            $table->integer('page_end')->nullable()->comment('For reading tasks: ending page');
            
            // Audit fields
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Indexes for performance
            $table->index(['program_id', 'task_type']);
            $table->index(['start_date', 'end_date']);
            $table->index(['is_recurring', 'recurrence_pattern']);
            $table->index('book_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('program_tasks');
    }
};
