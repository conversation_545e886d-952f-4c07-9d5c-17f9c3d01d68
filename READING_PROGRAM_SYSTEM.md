# Gamified Reading Program System

## Overview

This document describes the comprehensive gamified reading program system implemented for the Laravel 12 + Moonshine 3 application. The system allows hierarchical program creation and management across different organizational levels with full gamification features.

## System Architecture

### Hierarchical Structure
- **School Group Administrators**: Create programs and select participating schools
- **School Administrators**: Select participating classes within their schools  
- **Teachers**: Create teams and manage individual students within their classes
- **Students**: Participate in programs, progress through story chapters, earn achievements, and customize character progression

## Database Tables

### 1. Core Program Table

#### `programs`
- **Purpose**: Core reading program definitions
- **Key Fields**:
  - `name` (string, required) - Program name
  - `description` (text, nullable) - Program description
  - `story_id` (foreign key) - Associated story
  - `start_date` (date, required) - Program start date
  - `end_date` (date, required) - Program end date
  - `is_active` (boolean, default true) - Program status
- **Relationships**: BelongsTo Story, HasMany Teams/UserLevels/UserAchievements/etc.

### 2. Program Association Tables

#### `program_schools`
- **Purpose**: Many-to-many relationship between programs and schools
- **Key Fields**: `program_id`, `organization_id`
- **Constraints**: Unique combination, organization must be type 'school'

#### `program_classes`
- **Purpose**: Many-to-many relationship between programs and classes
- **Key Fields**: `program_id`, `school_class_id`
- **Constraints**: Unique combination, class must be active

#### `program_books`
- **Purpose**: Many-to-many relationship between programs and books
- **Key Fields**: `program_id`, `book_id`
- **Constraints**: Unique combination

### 3. Team Management Tables

#### `program_teams`
- **Purpose**: Team definitions within programs
- **Key Fields**: `program_id`, `name`
- **Relationships**: BelongsTo Program, HasMany ProgramTeamMembers

#### `program_team_members`
- **Purpose**: Team membership tracking
- **Key Fields**: `program_team_id`, `term_user_id`
- **Constraints**: Unique combination, term_user must be student type

### 4. Student Progress Tracking Tables

#### `program_user_levels`
- **Purpose**: Student progress tracking through story chapters
- **Key Fields**: `program_id`, `term_user_id`, `story_chapter_id`
- **Constraints**: Unique program-user combination

#### `program_user_achievements`
- **Purpose**: Student achievement tracking
- **Key Fields**: `program_id`, `term_user_id`, `story_achievement_id`, `earned_at`
- **Constraints**: Unique program-user-achievement combination

#### `program_user_characters`
- **Purpose**: Student character progression and evolution
- **Key Fields**: `program_id`, `term_user_id`, `story_character_id`, `current_stage`
- **Features**: Character evolution through stages

#### `program_user_maps`
- **Purpose**: Custom map positioning for achievements/items
- **Key Fields**: `program_id`, `term_user_id`, `item_type`, `item_id`, `x_coordinate`, `y_coordinate`
- **Features**: Polymorphic item references, precise coordinate positioning

#### `program_user_points`
- **Purpose**: Student points log and tracking
- **Key Fields**: `program_id`, `term_user_id`, `point_source`, `points`, `earned_at`
- **Point Sources**: Achievement, Task, Quest, Reading, Bonus

## Models

### Core Models
- **Program**: Main program model with relationships and status methods
- **ProgramTeam**: Team management with member counting
- **ProgramUserLevel**: Progress tracking with chapter relationships
- **ProgramUserAchievement**: Achievement tracking with type filtering
- **ProgramUserCharacter**: Character progression with evolution methods
- **ProgramUserMap**: Map positioning with coordinate validation
- **ProgramUserPoint**: Points tracking with source categorization

### Pivot Models
- **ProgramSchool**: Program-school associations
- **ProgramClass**: Program-class associations  
- **ProgramBook**: Program-book associations
- **ProgramTeamMember**: Team membership management

## Moonshine Admin Resources

### Main Resources
1. **ProgramResource**: Core program management
2. **ProgramTeamResource**: Team creation and management
3. **ProgramUserLevelResource**: Student progress tracking
4. **ProgramUserAchievementResource**: Achievement management
5. **ProgramUserCharacterResource**: Character progression
6. **ProgramUserMapResource**: Map item positioning
7. **ProgramUserPointResource**: Points management

### Association Resources
8. **ProgramSchoolResource**: School participation management
9. **ProgramClassResource**: Class participation management
10. **ProgramBookResource**: Book association management
11. **ProgramTeamMemberResource**: Team membership management

## Features

### Program Management
- Date range validation (end_date > start_date)
- Status tracking (active, inactive, upcoming, completed)
- Duration calculation
- Hierarchical school/class selection

### Student Progress
- Chapter progression tracking
- Achievement earning with timestamps
- Character evolution through stages
- Custom map item positioning
- Points accumulation from multiple sources

### Gamification Elements
- **Achievements**: Items, badges, rewards, trophies, collectibles
- **Characters**: Multi-stage evolution system
- **Points**: Multiple earning sources with detailed logging
- **Maps**: Custom positioning for personalized experiences

### Validation Rules
- Programs must have valid date ranges
- Teams must belong to valid programs
- Students can only join teams if enrolled in participating classes
- Achievement earning validated against story progression
- Map coordinates validated within story boundaries

## Admin Interface

### Menu Organization
All resources are organized under "Reading Programs" menu group in Moonshine admin:
- Programs (main management)
- Program Schools (school participation)
- Program Classes (class participation)  
- Program Books (book associations)
- Program Teams (team management)
- Program Team Members (membership)
- Program User Levels (progress tracking)
- Program User Achievements (achievement management)
- Program User Characters (character progression)
- Program User Maps (map customization)
- Program User Points (points management)

### Localization
Full Turkish and English translation support for:
- All field labels and descriptions
- Menu items and resource names
- Validation messages
- Status indicators
- Point sources and item types

## Technical Implementation

### Database Features
- Soft deletes on all tables
- Comprehensive audit trails (created_by, updated_by, deleted_by)
- Optimized indexes for performance
- Foreign key constraints with proper cascading
- Unique constraints to prevent duplicates

### Model Features
- BaseModel inheritance with automatic audit field handling
- Proper relationship definitions
- Scope methods for common queries
- Attribute accessors for computed values
- Type casting for data integrity

### Admin Features
- BaseResource inheritance with common functionality
- Cancel buttons and redirect-to-index after save
- Comprehensive filtering and searching
- Proper field validation
- Relationship loading optimization

This system provides a complete foundation for gamified reading programs with comprehensive management tools for educators at all organizational levels.
