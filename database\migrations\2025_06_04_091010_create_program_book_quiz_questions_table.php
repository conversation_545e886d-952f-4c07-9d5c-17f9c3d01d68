<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('program_book_quiz_questions', function (Blueprint $table) {
            $table->id();
            
            // Core quiz question information
            $table->foreignId('program_book_quiz_id')->constrained('program_book_quizzes')->onDelete('cascade');
            $table->foreignId('book_question_id')->constrained('book_questions')->onDelete('cascade');
            
            // Question order and response
            $table->integer('question_order')->comment('Order of question in quiz');
            $table->string('student_answer')->nullable()->comment('Student\'s selected answer');
            $table->boolean('is_correct')->nullable()->comment('Whether answer was correct');
            $table->datetime('answered_at')->nullable()->comment('When question was answered');
            $table->integer('points_earned')->default(0)->comment('Points earned for this question');
            
            // Audit fields
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Indexes for performance
            $table->index(['program_book_quiz_id', 'question_order'], 'quiz_question_order_idx');
            $table->index(['book_question_id', 'is_correct'], 'question_correct_idx');
            $table->index(['is_correct', 'points_earned'], 'correct_points_idx');
            $table->index('answered_at');

            // Unique constraint to prevent duplicate questions in same quiz
            $table->unique(['program_book_quiz_id', 'book_question_id'], 'unique_quiz_question');

            // Unique constraint for question order within quiz
            $table->unique(['program_book_quiz_id', 'question_order'], 'unique_quiz_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('program_book_quiz_questions');
    }
};
