<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProgramUserAchievement extends BaseModel
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'program_user_achievements';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_id',
        'term_user_id',
        'story_achievement_id',
        'earned_at',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'earned_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the program.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the term user (student).
     */
    public function termUser(): BelongsTo
    {
        return $this->belongsTo(TermUser::class);
    }

    /**
     * Get the story achievement.
     */
    public function storyAchievement(): BelongsTo
    {
        return $this->belongsTo(StoryAchievement::class);
    }

    /**
     * Scope to get recent achievements.
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('earned_at', '>=', now()->subDays($days));
    }

    /**
     * Scope to get achievements by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->whereHas('storyAchievement', function ($q) use ($type) {
            $q->where('type', $type);
        });
    }
}
