<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            // Core system data
            RoleSeeder::class,
            AdminUserSeeder::class,

            // Basic test data (organizations, users, books)
            TestDataSeeder::class,

            // Gamification system (stories, characters, achievements)
            GamificationSeeder::class,

            // Program system
            ProgramTestDataSeeder::class,

            // Story-book associations
            StoryBookTestDataSeeder::class,

            // Assessment system
            AssessmentSystemSeeder::class,

            // Reading log system
            ReadingLogSystemSeeder::class,

            // Program tasks
            ProgramTaskSeeder::class,

            // Program user books
            ProgramUserBookTestDataSeeder::class,
        ]);
    }
}
