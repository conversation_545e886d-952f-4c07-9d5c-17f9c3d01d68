<?php

namespace Database\Seeders;

use App\Models\Book;
use App\Models\Story;
use App\Models\StoryBook;
use App\Models\User;
use Illuminate\Database\Seeder;

class StoryBookTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user as creator
        $user = User::first();
        
        if (!$user) {
            $this->command->error('No users found. Please create a user first.');
            return;
        }

        // Get the first story
        $story = Story::first();
        
        if (!$story) {
            $this->command->error('No stories found. Please create a story first.');
            return;
        }

        // Get some books
        $books = Book::take(5)->get();
        
        if ($books->count() < 3) {
            $this->command->error('Need at least 3 books to create story book associations.');
            return;
        }

        // Create story book associations with sequences
        $storyBooks = [
            [
                'story_id' => $story->id,
                'book_id' => $books[0]->id,
                'sequence' => 1,
                'created_by' => $user->id,
            ],
            [
                'story_id' => $story->id,
                'book_id' => $books[1]->id,
                'sequence' => 2,
                'created_by' => $user->id,
            ],
            [
                'story_id' => $story->id,
                'book_id' => $books[2]->id,
                'sequence' => 3,
                'created_by' => $user->id,
            ],
        ];

        // Add more books if available
        if ($books->count() >= 4) {
            $storyBooks[] = [
                'story_id' => $story->id,
                'book_id' => $books[3]->id,
                'sequence' => 4,
                'created_by' => $user->id,
            ];
        }

        if ($books->count() >= 5) {
            $storyBooks[] = [
                'story_id' => $story->id,
                'book_id' => $books[4]->id,
                'sequence' => 5,
                'created_by' => $user->id,
            ];
        }

        foreach ($storyBooks as $storyBookData) {
            // Check if this association already exists
            $exists = StoryBook::where('story_id', $storyBookData['story_id'])
                              ->where('book_id', $storyBookData['book_id'])
                              ->exists();
            
            if (!$exists) {
                StoryBook::create($storyBookData);
                $book = Book::find($storyBookData['book_id']);
                $this->command->info("Associated book '{$book->name}' with story '{$story->title}' at sequence {$storyBookData['sequence']}");
            } else {
                $book = Book::find($storyBookData['book_id']);
                $this->command->info("Book '{$book->name}' already associated with story '{$story->title}'");
            }
        }

        $this->command->info('Story book test data seeded successfully!');
    }
}
