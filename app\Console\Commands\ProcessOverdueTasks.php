<?php

namespace App\Console\Commands;

use App\Services\TaskManagementService;
use Illuminate\Console\Command;

class ProcessOverdueTasks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tasks:process-overdue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mark overdue tasks as missed automatically';

    /**
     * Execute the console command.
     */
    public function handle(TaskManagementService $taskService): int
    {
        $this->info('Processing overdue tasks...');
        
        $markedCount = $taskService->processOverdueTasks();
        
        if ($markedCount > 0) {
            $this->info("Marked {$markedCount} overdue tasks as missed.");
        } else {
            $this->info('No overdue tasks found.');
        }
        
        return Command::SUCCESS;
    }
}
