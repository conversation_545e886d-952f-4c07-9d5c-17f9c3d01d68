<?php if($item->exists): ?>

    <?php if (isset($component)) { $__componentOriginal7bab788aed2a0e8938609bb9914a586d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7bab788aed2a0e8938609bb9914a586d = $attributes; } ?>
<?php $component = MoonShine\UI\Components\Layout\Divider::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('moonshine::layout.divider'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\MoonShine\UI\Components\Layout\Divider::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7bab788aed2a0e8938609bb9914a586d)): ?>
<?php $attributes = $__attributesOriginal7bab788aed2a0e8938609bb9914a586d; ?>
<?php unset($__attributesOriginal7bab788aed2a0e8938609bb9914a586d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7bab788aed2a0e8938609bb9914a586d)): ?>
<?php $component = $__componentOriginal7bab788aed2a0e8938609bb9914a586d; ?>
<?php unset($__componentOriginal7bab788aed2a0e8938609bb9914a586d); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal1c4a610d72753694a16003f411d4b6e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1c4a610d72753694a16003f411d4b6e5 = $attributes; } ?>
<?php $component = MoonShine\UI\Components\Title::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('moonshine::title'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\MoonShine\UI\Components\Title::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mb-6']); ?>
        <?php echo e($label); ?>

     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1c4a610d72753694a16003f411d4b6e5)): ?>
<?php $attributes = $__attributesOriginal1c4a610d72753694a16003f411d4b6e5; ?>
<?php unset($__attributesOriginal1c4a610d72753694a16003f411d4b6e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1c4a610d72753694a16003f411d4b6e5)): ?>
<?php $component = $__componentOriginal1c4a610d72753694a16003f411d4b6e5; ?>
<?php unset($__componentOriginal1c4a610d72753694a16003f411d4b6e5); ?>
<?php endif; ?>

    <?php echo e($form->render()); ?>


<?php endif; ?>
<?php /**PATH D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\vendor\sweet1s\moonshine-roles-permissions\src\Providers/../../resources/views/form-components/permissions.blade.php ENDPATH**/ ?>