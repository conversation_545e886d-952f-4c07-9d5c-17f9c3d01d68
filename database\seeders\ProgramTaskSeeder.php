<?php

namespace Database\Seeders;

use App\Models\Program;
use App\Models\ProgramTask;
use App\Models\ProgramTaskInstance;
use App\Models\Book;
use App\Models\User;
use App\Services\TaskManagementService;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class ProgramTaskSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding program tasks...');
        
        // Get first active program
        $program = Program::where('is_active', true)->first();
        
        if (!$program) {
            $this->command->warn('No active program found. Creating sample program...');
            return;
        }
        
        // Get some books for reading tasks
        $books = Book::limit(3)->get();
        
        if ($books->isEmpty()) {
            $this->command->warn('No books found. Please seed books first.');
            return;
        }
        
        // Create sample tasks
        $tasks = [
            [
                'name' => 'Daily Reading Log',
                'description' => 'Record your daily reading progress and thoughts.',
                'task_type' => ProgramTask::TYPE_READING_LOG,
                'is_recurring' => true,
                'recurrence_pattern' => ProgramTask::RECURRENCE_DAILY,
                'start_date' => now()->toDateString(),
                'end_date' => now()->addDays(30)->toDateString(),
                'points' => 10,
                'book_id' => $books->first()->id,
                'page_start' => 1,
                'page_end' => 50,
            ],
            [
                'name' => 'Weekly Reading Comprehension',
                'description' => 'Answer comprehension questions about your weekly reading.',
                'task_type' => ProgramTask::TYPE_QUESTION,
                'is_recurring' => true,
                'recurrence_pattern' => ProgramTask::RECURRENCE_WEEKLY,
                'start_date' => now()->toDateString(),
                'end_date' => now()->addDays(28)->toDateString(),
                'points' => 25,
                'book_id' => $books->first()->id,
            ],
            [
                'name' => 'Book Review Activity',
                'description' => 'Write a detailed review of the assigned book.',
                'task_type' => ProgramTask::TYPE_ACTIVITY,
                'is_recurring' => false,
                'start_date' => now()->toDateString(),
                'end_date' => now()->addDays(14)->toDateString(),
                'points' => 50,
                'book_id' => $books->get(1)->id,
            ],
            [
                'name' => 'Reading Corner Setup',
                'description' => 'Create a comfortable reading space at home and share a photo.',
                'task_type' => ProgramTask::TYPE_PHYSICAL,
                'is_recurring' => false,
                'start_date' => now()->toDateString(),
                'end_date' => now()->addDays(7)->toDateString(),
                'points' => 30,
            ],
            [
                'name' => 'Character Analysis',
                'description' => 'Analyze the main character of your current book.',
                'task_type' => ProgramTask::TYPE_ACTIVITY,
                'is_recurring' => false,
                'start_date' => now()->addDays(3)->toDateString(),
                'end_date' => now()->addDays(10)->toDateString(),
                'points' => 40,
                'book_id' => $books->get(2)->id,
                'page_start' => 1,
                'page_end' => 100,
            ],
        ];
        
        $createdTasks = [];
        
        foreach ($tasks as $taskData) {
            $taskData['program_id'] = $program->id;
            $task = ProgramTask::create($taskData);
            $createdTasks[] = $task;
            
            $this->command->info("Created task: {$task->name}");
        }
        
        // Get some students to assign tasks to
        $students = User::whereHas('termUsers', function($query) use ($program) {
            $query->whereHas('role', function($roleQuery) {
                $roleQuery->where('level', 5); // Student level
            });
        })->limit(5)->get();
        
        if ($students->isEmpty()) {
            $this->command->warn('No students found. Tasks created but not assigned.');
            return;
        }
        
        // Assign tasks to students
        $taskService = new TaskManagementService();
        $totalAssignments = 0;
        
        foreach ($createdTasks as $task) {
            $studentIds = $students->pluck('id')->toArray();
            $assignedCount = $taskService->assignTask($task, $studentIds);
            $totalAssignments += $assignedCount;
            
            $this->command->info("Assigned '{$task->name}' to {$assignedCount} students");
        }
        
        // Complete some tasks for demonstration
        $instances = ProgramTaskInstance::where('status', 'pending')
            ->limit(3)
            ->get();
        
        $completedCount = 0;
        foreach ($instances as $instance) {
            if ($taskService->completeTask($instance, $students->first()->id, 'Sample completion')) {
                $completedCount++;
            }
        }
        
        $this->command->info("Completed {$completedCount} sample tasks");
        
        // Display statistics
        $stats = $taskService->getTaskStatistics($program->id);
        
        $this->command->info('Task Statistics:');
        $this->command->info("- Total instances: {$stats['total']}");
        $this->command->info("- Pending: {$stats['pending']}");
        $this->command->info("- Completed: {$stats['completed']}");
        $this->command->info("- Missed: {$stats['missed']}");
        $this->command->info("- Excused: {$stats['excused']}");
        $this->command->info("- Overdue: {$stats['overdue']}");
        
        $this->command->info('Program task seeding completed successfully!');
    }
}
