<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Role;
use Symfony\Component\HttpFoundation\Response;

class TeacherAuthentication
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated with moonshine guard
        if (!Auth::guard('moonshine')->check()) {
            return redirect()->route('teacher.login');
        }

        $user = Auth::guard('moonshine')->user();

        // Check if user has teacher role (level 3)
        if (!$user->isTeacher()) {
            Auth::guard('moonshine')->logout();
            return redirect()->route('teacher.login')
                ->withErrors(['error' => __('teacher.access_denied')]);
        }

        return $next($request);
    }
}
