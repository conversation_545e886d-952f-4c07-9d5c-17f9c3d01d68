<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

class Tag extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'tag_type',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'tag_type' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Tag type constants.
     */
    const TYPE_ORGANIZATION = 1;
    const TYPE_BOOKS = 2;
    const TYPE_USERS = 3;

    /**
     * Get all tag types.
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_ORGANIZATION => 'Organization',
            self::TYPE_BOOKS => 'Books',
            self::TYPE_USERS => 'Users',
        ];
    }

    /**
     * Get tag type name.
     */
    public function getTypeNameAttribute(): string
    {
        return self::getTypes()[$this->tag_type] ?? 'Unknown';
    }

    /**
     * Get tag values for this tag.
     */
    public function tagValues(): HasMany
    {
        return $this->hasMany(TagValue::class);
    }

    /**
     * Scope to filter by tag type.
     */
    public function scopeByType($query, int $type)
    {
        return $query->where('tag_type', $type);
    }

    /**
     * Scope to get organization tags.
     */
    public function scopeOrganizationTags($query)
    {
        return $query->where('tag_type', self::TYPE_ORGANIZATION);
    }

    /**
     * Scope to get book tags.
     */
    public function scopeBookTags($query)
    {
        return $query->where('tag_type', self::TYPE_BOOKS);
    }

    /**
     * Scope to get user tags.
     */
    public function scopeUserTags($query)
    {
        return $query->where('tag_type', self::TYPE_USERS);
    }

    /**
     * Get organizations tagged with this tag.
     */
    public function getOrganizationsAttribute()
    {
        if ($this->tag_type !== self::TYPE_ORGANIZATION) {
            return collect();
        }

        return Organization::whereIn('id', 
            $this->tagValues()
                 ->where('taggable_type', TagValue::TYPE_ORGANIZATION)
                 ->pluck('taggable_id')
        )->get();
    }

    /**
     * Get books tagged with this tag.
     */
    public function getBooksAttribute()
    {
        if ($this->tag_type !== self::TYPE_BOOKS) {
            return collect();
        }

        return Book::whereIn('id', 
            $this->tagValues()
                 ->where('taggable_type', TagValue::TYPE_BOOKS)
                 ->pluck('taggable_id')
        )->get();
    }

    /**
     * Get users tagged with this tag.
     */
    public function getUsersAttribute()
    {
        if ($this->tag_type !== self::TYPE_USERS) {
            return collect();
        }

        return User::whereIn('id', 
            $this->tagValues()
                 ->where('taggable_type', TagValue::TYPE_USERS)
                 ->pluck('taggable_id')
        )->get();
    }

    /**
     * Get usage count for this tag.
     */
    public function getUsageCountAttribute(): int
    {
        return $this->tagValues()->count();
    }

    /**
     * Check if this tag is used.
     */
    public function isUsed(): bool
    {
        return $this->usage_count > 0;
    }
}
