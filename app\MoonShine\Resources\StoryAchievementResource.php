<?php

namespace App\MoonShine\Resources;

use App\Models\StoryAchievement;
use App\Models\Story;
use App\Models\StoryRule;
use App\Models\Role;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\{Text, Textarea, Image, Select, Number, Switcher};
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;

#[Icon('trophy')]
class StoryAchievementResource extends BaseResource
{
    protected string $model = StoryAchievement::class;

    protected string $column = 'name';

    protected array $with = ['story', 'unlockRule', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.story_achievements');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.story'), 'story', 
                formatted: fn(Story $story) => $story->title,
                resource: StoryResource::class)
                ->sortable(),
            
            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Text::make(__('admin.type'), 'type_display_name')
                ->sortable(),
            
            Image::make(__('admin.image'), 'image'),
            
            Text::make(__('admin.info'), 'info'),
            
            BelongsTo::make(__('admin.unlock_rule'), 'unlockRule', 
                formatted: fn(?StoryRule $rule) => $rule?->description ?? 'No rule',
                resource: StoryRuleResource::class)
                ->nullable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        BelongsTo::make(__('admin.story'), 'story', 
                            formatted: fn(Story $story) => $story->title,
                            resource: StoryResource::class)
                            ->required()
                            ->placeholder(__('admin.select_story')),
                        
                        Flex::make([
                            Text::make(__('admin.name'), 'name')
                                ->required()
                                ->placeholder(__('admin.enter_name')),
                            
                            Select::make(__('admin.type'), 'type')
                                ->required()
                                ->options(StoryAchievement::getTypes())
                                ->placeholder(__('admin.select_achievement_type')),
                        ]),
                        
                        Textarea::make(__('admin.description'), 'description')
                            ->required()
                            ->placeholder(__('admin.enter_description')),
                        
                        Image::make(__('admin.image'), 'image')
                            ->required(),
                        
                        BelongsTo::make(__('admin.unlock_rule'), 'unlockRule', 
                            formatted: fn(?StoryRule $rule) => $rule?->description ?? 'No rule',
                            resource: StoryRuleResource::class)
                            ->nullable()
                            ->placeholder(__('admin.select_unlock_rule')),
                    ]),
                    
                    Tab::make(__('admin.map_coordinates'), [
                        Flex::make([
                            Number::make(__('admin.map_start_x'), 'map_start_x')
                                ->nullable()
                                ->min(0),
                            
                            Number::make(__('admin.map_start_y'), 'map_start_y')
                                ->nullable()
                                ->min(0),
                        ]),
                        
                        Flex::make([
                            Number::make(__('admin.map_end_x'), 'map_end_x')
                                ->nullable()
                                ->min(0),
                            
                            Number::make(__('admin.map_end_y'), 'map_end_y')
                                ->nullable()
                                ->min(0),
                        ]),
                        
                        Switcher::make(__('admin.is_dynamic_position'), 'is_dynamic_position')
                            ->default(false),
                    ]),
                    
                    Tab::make(__('admin.summary'), [
                        Text::make(__('admin.type_display_name'), 'type_display_name')
                            ->readonly(),
                        
                        Text::make(__('admin.info'), 'info')
                            ->readonly(),
                    ]),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(__('admin.story'), 'story', 
                formatted: fn(Story $story) => $story->title,
                resource: StoryResource::class),
            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.type'), 'type_display_name'),
            Textarea::make(__('admin.description'), 'description'),
            Image::make(__('admin.image'), 'image'),
            BelongsTo::make(__('admin.unlock_rule'), 'unlockRule', 
                formatted: fn(?StoryRule $rule) => $rule?->description ?? 'No rule',
                resource: StoryRuleResource::class),
            Number::make(__('admin.map_start_x'), 'map_start_x'),
            Number::make(__('admin.map_start_y'), 'map_start_y'),
            Number::make(__('admin.map_end_x'), 'map_end_x'),
            Number::make(__('admin.map_end_y'), 'map_end_y'),
            Switcher::make(__('admin.is_dynamic_position'), 'is_dynamic_position'),
            Text::make(__('admin.info'), 'info'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'story_id' => ['required', 'exists:stories,id'],
            'name' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'type' => ['required', 'string', 'max:255'],
            'image' => ['required', 'string', 'max:255'],
            'unlock_rule_id' => ['nullable', 'exists:story_rules,id'],
            'map_start_x' => ['nullable', 'integer', 'min:0'],
            'map_start_y' => ['nullable', 'integer', 'min:0'],
            'map_end_x' => ['nullable', 'integer', 'min:0'],
            'map_end_y' => ['nullable', 'integer', 'min:0'],
            'is_dynamic_position' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['name', 'description'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
