<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProgramUserCharacter extends BaseModel
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'program_user_characters';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_id',
        'term_user_id',
        'story_character_id',
        'current_stage',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'current_stage' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the program.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the term user (student).
     */
    public function termUser(): BelongsTo
    {
        return $this->belongsTo(TermUser::class);
    }

    /**
     * Get the story character.
     */
    public function storyCharacter(): BelongsTo
    {
        return $this->belongsTo(StoryCharacter::class);
    }

    /**
     * Get the current character stage.
     */
    public function getCurrentStageDetailsAttribute()
    {
        return $this->storyCharacter->stages()
                    ->where('stage_number', $this->current_stage)
                    ->first();
    }

    /**
     * Check if character can evolve to next stage.
     */
    public function canEvolve(): bool
    {
        $maxStage = $this->storyCharacter->stages()->max('stage_number');
        return $this->current_stage < $maxStage;
    }

    /**
     * Evolve character to next stage.
     */
    public function evolve(): bool
    {
        if ($this->canEvolve()) {
            $this->current_stage++;
            return $this->save();
        }
        return false;
    }
}
