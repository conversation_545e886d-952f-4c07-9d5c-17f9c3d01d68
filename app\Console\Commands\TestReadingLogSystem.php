<?php

namespace App\Console\Commands;

use App\Services\ReadingLogService;
use App\Models\ProgramReadingLog;
use App\Models\ProgramTask;
use App\Models\ProgramTaskInstance;
use App\Models\Program;
use App\Models\Book;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class TestReadingLogSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:reading-log-system';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test that the reading log system is working correctly';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Testing Reading Log System...');
        
        try {
            // Test models
            $this->testModels();
            
            // Test reading log service
            $this->testReadingLogService();
            
            // Test reading log creation
            $this->testReadingLogCreation();
            
            // Test verification system
            $this->testVerificationSystem();
            
            // Test statistics and analytics
            $this->testStatistics();
            
            // Display comprehensive statistics
            $this->displayComprehensiveStatistics();
            
            $this->info('');
            $this->info('🎉 All reading log system components are working correctly!');
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('❌ Error testing reading log system: ' . $e->getMessage());
            $this->error('   File: ' . $e->getFile() . ':' . $e->getLine());
            return Command::FAILURE;
        }
    }

    /**
     * Test model functionality.
     */
    private function testModels(): void
    {
        $this->info('Testing Models...');
        
        // Test ProgramReadingLog model
        $logCount = ProgramReadingLog::count();
        $this->info("✅ ProgramReadingLog model - {$logCount} logs found");
        
        if ($logCount > 0) {
            $log = ProgramReadingLog::with(['user', 'book', 'program'])->first();
            $this->info("   Sample log: {$log->user->name} read {$log->pages_read} pages of '{$log->book->name}'");
            $this->info("   Reading date: {$log->reading_date->format('Y-m-d')}");
            $this->info("   Page range: {$log->start_page}-{$log->end_page}");
            $this->info("   Duration: " . ($log->reading_duration_minutes ?? 'Not recorded') . " minutes");
            $this->info("   Points awarded: {$log->points_awarded}");
            $this->info("   Verified: " . ($log->is_verified ? 'Yes' : 'No'));
            
            // Test progress calculation
            $progress = $log->calculateProgress();
            $this->info("   Progress: {$progress['total_pages_read']} total pages, {$progress['total_sessions']} sessions");
            $this->info("   Reading streak: {$progress['reading_streak']} days");
        }
        
        // Test reading log tasks
        $readingTasks = ProgramTask::where('task_type', ProgramTask::TYPE_READING_LOG)->count();
        $this->info("✅ Reading log tasks - {$readingTasks} tasks found");
        
        $taskInstances = ProgramTaskInstance::whereHas('programTask', function($query) {
            $query->where('task_type', ProgramTask::TYPE_READING_LOG);
        })->count();
        $this->info("✅ Reading log task instances - {$taskInstances} instances found");
    }

    /**
     * Test reading log service.
     */
    private function testReadingLogService(): void
    {
        $this->info('');
        $this->info('Testing ReadingLogService...');
        
        $readingLogService = new ReadingLogService();
        
        // Get test data
        $program = Program::where('is_active', true)->first();
        $book = Book::first();
        $student = User::whereHas('termUsers', function($query) {
            $query->whereHas('role', function($roleQuery) {
                $roleQuery->where('level', 5);
            });
        })->first();
        
        if (!$program || !$book || !$student) {
            $this->warn('No program, book, or student found for service testing');
            return;
        }
        
        // Test student statistics
        $stats = $readingLogService->getStudentReadingStatistics($program->id, $student->id);
        $this->info("✅ Student statistics generated");
        $this->info("   Total sessions: {$stats['total_sessions']}");
        $this->info("   Total pages read: {$stats['total_pages_read']}");
        $this->info("   Total points earned: {$stats['total_points_earned']}");
        $this->info("   Current reading streak: {$stats['current_reading_streak']}");
        
        // Test reading streak
        $streak = $readingLogService->getReadingStreak($program->id, $book->id, $student->id);
        $this->info("✅ Reading streak calculated");
        $this->info("   Current streak: {$streak['current_streak']} days");
        $this->info("   Longest streak: {$streak['longest_streak']} days");
        $this->info("   Last reading: " . ($streak['last_reading_date'] ? $streak['last_reading_date']->format('Y-m-d') : 'Never'));
        
        // Test dashboard data
        $dashboard = $readingLogService->getReadingLogDashboard($program->id, now()->subDays(60), now());
        $this->info("✅ Dashboard data generated");
        $this->info("   Total logs (60 days): {$dashboard['total_logs']}");
        $this->info("   Unique students: {$dashboard['unique_students']}");
        $this->info("   Average pages per session: {$dashboard['average_pages_per_session']}");
    }

    /**
     * Test reading log creation.
     */
    private function testReadingLogCreation(): void
    {
        $this->info('');
        $this->info('Testing Reading Log Creation...');
        
        $program = Program::where('is_active', true)->first();
        $book = Book::first();
        $student = User::whereHas('termUsers', function($query) {
            $query->whereHas('role', function($roleQuery) {
                $roleQuery->where('level', 5);
            });
        })->first();
        
        if (!$program || !$book || !$student) {
            $this->warn('No program, book, or student found for creation testing');
            return;
        }
        
        $readingLogService = new ReadingLogService();
        
        // Test validation
        $validation = ProgramReadingLog::canCreateEntry(
            $program->id, 
            $book->id, 
            $student->id, 
            now()
        );
        
        $this->info("✅ Entry validation checked");
        $this->info("   Can create today's entry: " . ($validation['can_create'] ? 'Yes' : 'No'));
        if (!$validation['can_create']) {
            $this->info("   Reason: {$validation['reason']}");
        }
        
        // Test future date validation
        $futureValidation = ProgramReadingLog::canCreateEntry(
            $program->id, 
            $book->id, 
            $student->id, 
            now()->addDay()
        );
        
        $this->info("✅ Future date validation checked");
        $this->info("   Can create tomorrow's entry: " . ($futureValidation['can_create'] ? 'Yes' : 'No'));
        $this->info("   Reason: " . ($futureValidation['reason'] ?? 'Valid'));
    }

    /**
     * Test verification system.
     */
    private function testVerificationSystem(): void
    {
        $this->info('');
        $this->info('Testing Verification System...');
        
        $unverifiedLogs = ProgramReadingLog::unverified()->count();
        $verifiedLogs = ProgramReadingLog::verified()->count();
        
        $this->info("✅ Verification status tracking");
        $this->info("   Unverified logs: {$unverifiedLogs}");
        $this->info("   Verified logs: {$verifiedLogs}");
        
        if ($unverifiedLogs > 0) {
            $log = ProgramReadingLog::unverified()->first();
            $this->info("   Sample unverified log: {$log->user->name} - {$log->reading_date->format('Y-m-d')}");
        }
        
        if ($verifiedLogs > 0) {
            $log = ProgramReadingLog::verified()->with('verifier')->first();
            $verifierName = $log->verifier ? $log->verifier->name : 'Unknown';
            $this->info("   Sample verified log: {$log->user->name} - verified by {$verifierName}");
        }
    }

    /**
     * Test statistics and analytics.
     */
    private function testStatistics(): void
    {
        $this->info('');
        $this->info('Testing Statistics and Analytics...');
        
        $program = Program::where('is_active', true)->first();
        
        if (!$program) {
            $this->warn('No active program found for statistics testing');
            return;
        }
        
        $readingLogService = new ReadingLogService();
        
        // Test date range logs
        $logs = $readingLogService->getReadingLogsForDateRange(
            $program->id,
            now()->subDays(30),
            now()
        );
        
        $this->info("✅ Date range logs retrieved: " . count($logs) . " logs");
        
        if (!empty($logs)) {
            $firstLog = $logs[0];
            $this->info("   Sample log: {$firstLog['student_name']} read {$firstLog['pages_read']} pages");
        }
        
        // Test dashboard with extended range
        $dashboard = $readingLogService->getReadingLogDashboard(
            $program->id,
            now()->subDays(60),
            now()
        );
        
        $this->info("✅ Extended dashboard data");
        $this->info("   Daily breakdown entries: " . count($dashboard['daily_breakdown']));
        $this->info("   Top readers: " . count($dashboard['top_readers']));
        $this->info("   Book popularity entries: " . count($dashboard['book_popularity']));
    }

    /**
     * Display comprehensive statistics.
     */
    private function displayComprehensiveStatistics(): void
    {
        $this->info('');
        $this->info('Reading Log System Statistics:');
        
        // Basic counts
        $this->info('📊 Database Counts:');
        $this->info("   Total reading logs: " . ProgramReadingLog::count());
        $this->info("   Reading log tasks: " . ProgramTask::where('task_type', ProgramTask::TYPE_READING_LOG)->count());
        $this->info("   Task instances: " . ProgramTaskInstance::whereHas('programTask', function($query) {
            $query->where('task_type', ProgramTask::TYPE_READING_LOG);
        })->count());
        
        // Verification statistics
        $this->info('✅ Verification Status:');
        $this->info("   Verified logs: " . ProgramReadingLog::where('is_verified', true)->count());
        $this->info("   Unverified logs: " . ProgramReadingLog::where('is_verified', false)->count());
        
        // Reading activity
        $this->info('📚 Reading Activity:');
        $totalPages = ProgramReadingLog::sum('pages_read');
        $totalPoints = ProgramReadingLog::sum('points_awarded');
        $avgPages = ProgramReadingLog::avg('pages_read');
        $avgDuration = ProgramReadingLog::whereNotNull('reading_duration_minutes')->avg('reading_duration_minutes');
        
        $this->info("   Total pages read: {$totalPages}");
        $this->info("   Total points awarded: {$totalPoints}");
        $this->info("   Average pages per session: " . round($avgPages, 1));
        $this->info("   Average duration: " . round($avgDuration, 1) . " minutes");
        
        // Recent activity
        $recentLogs = ProgramReadingLog::with(['user', 'book'])
                                     ->orderBy('reading_date', 'desc')
                                     ->limit(5)
                                     ->get();
        
        if ($recentLogs->isNotEmpty()) {
            $this->info('📅 Recent Activity:');
            foreach ($recentLogs as $log) {
                $verified = $log->is_verified ? '✅' : '⏳';
                $this->info("   {$verified} {$log->user->name}: {$log->pages_read} pages on {$log->reading_date->format('M j')}");
            }
        }
        
        // Date range analysis
        $this->info('📈 Date Range Analysis:');
        $last7Days = ProgramReadingLog::where('reading_date', '>=', now()->subDays(7))->count();
        $last30Days = ProgramReadingLog::where('reading_date', '>=', now()->subDays(30))->count();
        $last60Days = ProgramReadingLog::where('reading_date', '>=', now()->subDays(60))->count();
        
        $this->info("   Last 7 days: {$last7Days} logs");
        $this->info("   Last 30 days: {$last30Days} logs");
        $this->info("   Last 60 days: {$last60Days} logs");
        
        // Student participation
        $uniqueStudents = ProgramReadingLog::distinct('user_id')->count();
        $uniqueBooks = ProgramReadingLog::distinct('book_id')->count();
        
        $this->info('👥 Participation:');
        $this->info("   Students with reading logs: {$uniqueStudents}");
        $this->info("   Books being read: {$uniqueBooks}");
    }
}
