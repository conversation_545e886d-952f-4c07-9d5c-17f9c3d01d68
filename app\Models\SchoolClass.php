<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SchoolClass extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'organization_id',
        'grade_level_id',
        'created_by',
        'updated_by',
    ];

    /**
     * Get the organization this class belongs to.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the grade level of this class.
     */
    public function gradeLevel(): BelongsTo
    {
        return $this->belongsTo(GradeLevel::class);
    }

    /**
     * Get term users assigned to this class.
     */
    public function termUsers(): HasMany
    {
        return $this->hasMany(TermUser::class, 'class_id');
    }

    /**
     * Get active term users assigned to this class.
     */
    public function activeTermUsers(): HasMany
    {
        return $this->hasMany(TermUser::class, 'class_id')->where('active', true);
    }

    /**
     * Scope to filter by organization.
     */
    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Scope to filter by grade level.
     */
    public function scopeByGradeLevel($query, $gradeLevelId)
    {
        return $query->where('grade_level_id', $gradeLevelId);
    }

    
    /**
     * Get the display name for the class.
     */
/*    
    public function getDisplayNameAttribute(): string
    {
        return $this->full_name . ' (' . $this->organization->name . ')';
    }
*/
    /**
     * Get students in this class for the active term.
     */
    public function getStudentsAttribute()
    {
        return $this->activeTermUsers()
        /*
                    ->whereHas('role', function ($query) {
                        $query->where('level', Role::LEVEL_STUDENT);
                    })
                        */
                    ->with('user')
                    ->get()
                    ->pluck('user');
    }

    /**
     * Get teachers assigned to this class for the active term.
     */
    public function getTeachersAttribute()
    {
        return $this->activeTermUsers()
        /*
                    ->whereHas('role', function ($query) {
                        $query->where('level', Role::LEVEL_TEACHER);
                    })
                        */
                    ->with('user')
                    ->get()
                    ->pluck('user');
    }

    /**
     * Get student count for this class.
     */
    public function getStudentCountAttribute(): int
    {
        return $this->activeTermUsers()
        /*
                    ->whereHas('role', function ($query) {
                        $query->where('level', Role::LEVEL_STUDENT);
                    })
                        */
                    ->count();
    }
}
