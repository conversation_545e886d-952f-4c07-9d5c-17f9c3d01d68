<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\{<PERSON>, StoryRule, StoryRuleDetail, StoryChapter, StoryCharacter, StoryCharacterStage, StoryAchievement, User};

class GamificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user for audit fields
        $user = User::first();
        if (!$user) {
            $this->command->error('No users found. Please run UserSeeder first.');
            return;
        }

        // Create a sample story
        $story = Story::create([
            'title' => 'The Magical Library Adventure',
            'description' => 'Join our heroes as they explore the enchanted library, discovering magical books and solving ancient puzzles to unlock the secrets of reading.',
            'cover_image' => 'stories/magical-library-cover.jpg',
            'map_grid_rows' => 12,
            'map_grid_columns' => 15,
            'map_background_image' => 'stories/magical-library-map.jpg',
            'active' => true,
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);

        // Create story rules
        $pointsRule = StoryRule::create([
            'story_id' => $story->id,
            'rule_type' => StoryRule::TYPE_POINTS,
            'quantity' => 100,
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);

        $bookCountRule = StoryRule::create([
            'story_id' => $story->id,
            'rule_type' => StoryRule::TYPE_BOOK_COUNT,
            'quantity' => 3,
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);

        $achievementRule = StoryRule::create([
            'story_id' => $story->id,
            'rule_type' => StoryRule::TYPE_ACHIEVEMENT_COUNT,
            'quantity' => 2,
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);

        // Create story chapters
        $chapters = [
            [
                'title' => 'The Entrance Hall',
                'description' => 'Welcome to the magical library! Learn the basics of navigation and discover your first magical book.',
                'sequence' => 1,
                'map_start_x' => 1,
                'map_start_y' => 1,
                'map_end_x' => 3,
                'map_end_y' => 3,
                'unlock_rule_id' => null, // First chapter is always unlocked
            ],
            [
                'title' => 'The Fiction Wing',
                'description' => 'Explore the fiction section and meet the story characters who will guide your journey.',
                'sequence' => 2,
                'map_start_x' => 4,
                'map_start_y' => 2,
                'map_end_x' => 7,
                'map_end_y' => 5,
                'unlock_rule_id' => $pointsRule->id,
            ],
            [
                'title' => 'The Knowledge Tower',
                'description' => 'Climb the tower of knowledge and unlock advanced reading skills.',
                'sequence' => 3,
                'map_start_x' => 8,
                'map_start_y' => 6,
                'map_end_x' => 12,
                'map_end_y' => 10,
                'unlock_rule_id' => $bookCountRule->id,
            ],
            [
                'title' => 'The Secret Archive',
                'description' => 'Discover the hidden archive and unlock the ultimate reading treasures.',
                'sequence' => 4,
                'map_start_x' => 10,
                'map_start_y' => 11,
                'map_end_x' => 14,
                'map_end_y' => 14,
                'unlock_rule_id' => $achievementRule->id,
            ],
        ];

        foreach ($chapters as $chapterData) {
            StoryChapter::create([
                ...$chapterData,
                'story_id' => $story->id,
                'created_by' => $user->id,
                'updated_by' => $user->id,
            ]);
        }

        // Create story characters
        $characters = [
            [
                'name' => 'Luna the Librarian',
                'description' => 'A wise and magical librarian who guides students through their reading journey.',
                'base_image' => 'characters/luna-base.jpg',
                'active' => true,
            ],
            [
                'name' => 'Bookworm the Dragon',
                'description' => 'A friendly dragon who loves books and helps students discover new stories.',
                'base_image' => 'characters/bookworm-base.jpg',
                'active' => true,
            ],
            [
                'name' => 'Sage the Owl',
                'description' => 'An ancient owl with vast knowledge who teaches reading comprehension.',
                'base_image' => 'characters/sage-base.jpg',
                'active' => true,
            ],
        ];

        foreach ($characters as $characterData) {
            $character = StoryCharacter::create([
                ...$characterData,
                'story_id' => $story->id,
                'created_by' => $user->id,
                'updated_by' => $user->id,
            ]);

            // Create character stages
            $stages = [
                [
                    'stage_number' => 1,
                    'name' => 'Apprentice',
                    'image' => "characters/{$character->id}-apprentice.jpg",
                    'unlock_rule_id' => null,
                    'abilities' => ['basic_reading'],
                ],
                [
                    'stage_number' => 2,
                    'name' => 'Scholar',
                    'image' => "characters/{$character->id}-scholar.jpg",
                    'unlock_rule_id' => $pointsRule->id,
                    'abilities' => ['basic_reading', 'comprehension'],
                ],
                [
                    'stage_number' => 3,
                    'name' => 'Master',
                    'image' => "characters/{$character->id}-master.jpg",
                    'unlock_rule_id' => $bookCountRule->id,
                    'abilities' => ['basic_reading', 'comprehension', 'speed_reading'],
                ],
            ];

            foreach ($stages as $stageData) {
                StoryCharacterStage::create([
                    ...$stageData,
                    'character_id' => $character->id,
                    'created_by' => $user->id,
                    'updated_by' => $user->id,
                ]);
            }
        }

        // Create story achievements
        $achievements = [
            [
                'name' => 'First Book Badge',
                'description' => 'Awarded for reading your first book in the magical library.',
                'type' => StoryAchievement::TYPE_BADGE,
                'image' => 'achievements/first-book-badge.jpg',
                'unlock_rule_id' => null,
                'map_start_x' => 2,
                'map_start_y' => 2,
                'is_dynamic_position' => false,
            ],
            [
                'name' => 'Reading Streak Trophy',
                'description' => 'Earned by maintaining a consistent reading habit.',
                'type' => StoryAchievement::TYPE_TROPHY,
                'image' => 'achievements/reading-streak-trophy.jpg',
                'unlock_rule_id' => $pointsRule->id,
                'map_start_x' => 5,
                'map_start_y' => 3,
                'is_dynamic_position' => false,
            ],
            [
                'name' => 'Knowledge Crystal',
                'description' => 'A magical crystal that appears when you master comprehension skills.',
                'type' => StoryAchievement::TYPE_COLLECTIBLE,
                'image' => 'achievements/knowledge-crystal.jpg',
                'unlock_rule_id' => $bookCountRule->id,
                'map_start_x' => 9,
                'map_start_y' => 7,
                'is_dynamic_position' => true,
            ],
            [
                'name' => 'Master Reader Crown',
                'description' => 'The ultimate achievement for completing the entire reading adventure.',
                'type' => StoryAchievement::TYPE_REWARD,
                'image' => 'achievements/master-crown.jpg',
                'unlock_rule_id' => $achievementRule->id,
                'map_start_x' => 12,
                'map_start_y' => 12,
                'is_dynamic_position' => false,
            ],
        ];

        foreach ($achievements as $achievementData) {
            StoryAchievement::create([
                ...$achievementData,
                'story_id' => $story->id,
                'created_by' => $user->id,
                'updated_by' => $user->id,
            ]);
        }

        $this->command->info('Gamification sample data created successfully!');
        $this->command->info("Story: {$story->title}");
        $this->command->info("Chapters: {$story->chapter_count}");
        $this->command->info("Characters: {$story->character_count}");
        $this->command->info("Achievements: {$story->achievement_count}");
    }
}
