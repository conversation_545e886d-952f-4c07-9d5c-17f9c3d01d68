<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('program_task_actions', function (Blueprint $table) {
            $table->id();
            
            // Core action information
            $table->foreignId('program_task_instance_id')->constrained('program_task_instances')->onDelete('cascade');
            
            // Action details
            $table->enum('action_type', ['completed', 'missed', 'excused', 'reassigned'])
                  ->comment('Type of action');
            $table->datetime('action_date')->comment('When action occurred');
            
            // Optional details
            $table->text('notes')->nullable()->comment('Optional notes from teacher or system');
            $table->integer('points_awarded')->nullable()->comment('Points given for this action');
            $table->foreignId('completed_by')->nullable()->constrained('users')->onDelete('set null')
                  ->comment('Who marked as complete (teacher/student)');
            
            // Audit fields
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Indexes for performance
            $table->index(['program_task_instance_id', 'action_type']);
            $table->index(['action_date', 'action_type']);
            $table->index('completed_by');
            $table->index('points_awarded');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('program_task_actions');
    }
};
