<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use App\Models\User;

class TeacherController extends Controller
{
    /**
     * Show the teacher login form.
     */
    public function showLogin()
    {
        // If already authenticated as teacher, redirect to dashboard
        if (Auth::guard('moonshine')->check()) {
            $user = Auth::guard('moonshine')->user();
            if ($user instanceof User && $user->isTeacher()) {
                return redirect()->route('teacher.dashboard');
            }
        }

        return view('teacher.login');
    }

    /**
     * Handle teacher login.
     */
    public function login(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $credentials = $request->only('email', 'password');

        if (Auth::guard('moonshine')->attempt($credentials, $request->boolean('remember'))) {
            $user = Auth::guard('moonshine')->user();

            // Check if user is a teacher
            if ($user instanceof User && $user->isTeacher()) {
                $request->session()->regenerate();
                return redirect()->intended(route('teacher.dashboard'));
            } else {
                Auth::guard('moonshine')->logout();
                return back()->withErrors([
                    'email' => __('admin.teacher.invalid_credentials'),
                ]);
            }
        }

        return back()->withErrors([
            'email' => __('admin.teacher.invalid_credentials'),
        ]);
    }

    /**
     * Show the teacher dashboard.
     */
    public function dashboard(): View
    {
        $user = Auth::guard('moonshine')->user();
        
        return view('teacher.dashboard', [
            'user' => $user,
        ]);
    }

    /**
     * Handle teacher logout.
     */
    public function logout(Request $request): RedirectResponse
    {
        Auth::guard('moonshine')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('teacher.login');
    }
}
