<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('book_activity_types', function (Blueprint $table) {
            $table->id();
            
            // Activity type information
            $table->enum('category', ['vocabulary', 'spelling', 'writing', 'comprehension', 'creative'])
                  ->comment('Activity category');
            $table->string('name')->comment('Activity type name');
            $table->text('description')->nullable()->comment('Activity description');
            
            // Activity parameters
            $table->integer('min_word_count')->nullable()->comment('Minimum word count for writing activities');
            $table->integer('max_word_count')->nullable()->comment('Maximum word count for writing activities');
            $table->integer('points_base')->default(10)->comment('Base points for completing this activity type');
            
            // Status
            $table->boolean('is_active')->default(true);
            
            // Audit fields
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Indexes for performance
            $table->index(['category', 'is_active']);
            $table->index('is_active');
            $table->index('category');
            
            // Unique constraint for name within category
            $table->unique(['category', 'name'], 'unique_category_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('book_activity_types');
    }
};
