<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class BookActivityType extends BaseModel
{
    use SoftDeletes;

    /**
     * Activity category constants.
     */
    const CATEGORY_VOCABULARY = 'vocabulary';
    const CATEGORY_SPELLING = 'spelling';
    const CATEGORY_WRITING = 'writing';
    const CATEGORY_COMPREHENSION = 'comprehension';
    const CATEGORY_CREATIVE = 'creative';

    /**
     * The table associated with the model.
     */
    protected $table = 'book_activity_types';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'category',
        'name',
        'description',
        'min_word_count',
        'max_word_count',
        'points_base',
        'is_active',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'min_word_count' => 'integer',
            'max_word_count' => 'integer',
            'points_base' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get all activity categories.
     */
    public static function getCategories(): array
    {
        return [
            self::CATEGORY_VOCABULARY => 'Vocabulary',
            self::CATEGORY_SPELLING => 'Spelling',
            self::CATEGORY_WRITING => 'Writing',
            self::CATEGORY_COMPREHENSION => 'Comprehension',
            self::CATEGORY_CREATIVE => 'Creative',
        ];
    }

    /**
     * Get category name.
     */
    public function getCategoryNameAttribute(): string
    {
        return self::getCategories()[$this->category] ?? 'Unknown';
    }

    /**
     * Get the activities of this type.
     */
    public function activities(): HasMany
    {
        return $this->hasMany(ProgramBookActivity::class, 'activity_type_id');
    }

    /**
     * Scope to get active activity types.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeOfCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to get writing activities.
     */
    public function scopeWriting($query)
    {
        return $query->where('category', self::CATEGORY_WRITING);
    }

    /**
     * Scope to get vocabulary activities.
     */
    public function scopeVocabulary($query)
    {
        return $query->where('category', self::CATEGORY_VOCABULARY);
    }

    /**
     * Check if activity type has word count requirements.
     */
    public function hasWordCountRequirements(): bool
    {
        return !is_null($this->min_word_count) || !is_null($this->max_word_count);
    }

    /**
     * Check if activity type is writing-based.
     */
    public function isWritingBased(): bool
    {
        return $this->category === self::CATEGORY_WRITING || 
               $this->category === self::CATEGORY_CREATIVE;
    }

    /**
     * Get word count range display.
     */
    public function getWordCountRangeAttribute(): ?string
    {
        if ($this->min_word_count && $this->max_word_count) {
            return "{$this->min_word_count}-{$this->max_word_count} words";
        } elseif ($this->min_word_count) {
            return "Minimum {$this->min_word_count} words";
        } elseif ($this->max_word_count) {
            return "Maximum {$this->max_word_count} words";
        }
        return null;
    }

    /**
     * Validate word count for an activity.
     */
    public function validateWordCount(int $wordCount): array
    {
        $errors = [];
        
        if ($this->min_word_count && $wordCount < $this->min_word_count) {
            $errors[] = "Minimum {$this->min_word_count} words required. Current: {$wordCount}";
        }
        
        if ($this->max_word_count && $wordCount > $this->max_word_count) {
            $errors[] = "Maximum {$this->max_word_count} words allowed. Current: {$wordCount}";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'word_count' => $wordCount,
            'min_required' => $this->min_word_count,
            'max_allowed' => $this->max_word_count,
        ];
    }

    /**
     * Calculate points for an activity based on word count and quality.
     */
    public function calculatePoints(int $wordCount, float $qualityMultiplier = 1.0): int
    {
        $basePoints = $this->points_base;
        
        // Bonus points for exceeding minimum word count
        if ($this->min_word_count && $wordCount > $this->min_word_count) {
            $extraWords = $wordCount - $this->min_word_count;
            $bonusPoints = min($extraWords * 0.1, $basePoints * 0.5); // Max 50% bonus
            $basePoints += $bonusPoints;
        }
        
        // Apply quality multiplier
        $finalPoints = $basePoints * $qualityMultiplier;
        
        return (int) round($finalPoints);
    }

    /**
     * Get activity instructions based on type.
     */
    public function getInstructionsAttribute(): string
    {
        $instructions = $this->description ?? '';
        
        if ($this->hasWordCountRequirements()) {
            $instructions .= "\n\nWord Count: " . $this->word_count_range;
        }
        
        if ($this->points_base > 0) {
            $instructions .= "\n\nBase Points: {$this->points_base}";
        }
        
        return trim($instructions);
    }

    /**
     * Get activity types suitable for a book.
     */
    public static function getSuitableForBook(Book $book, ?string $category = null): array
    {
        $query = static::active();
        
        if ($category) {
            $query = $query->ofCategory($category);
        }
        
        return $query->orderBy('category')
                    ->orderBy('name')
                    ->get()
                    ->groupBy('category')
                    ->map(function($activities, $category) {
                        return [
                            'category' => $category,
                            'category_name' => self::getCategories()[$category] ?? $category,
                            'activities' => $activities->map(function($activity) {
                                return [
                                    'id' => $activity->id,
                                    'name' => $activity->name,
                                    'description' => $activity->description,
                                    'word_count_range' => $activity->word_count_range,
                                    'points_base' => $activity->points_base,
                                    'instructions' => $activity->instructions,
                                ];
                            })->toArray(),
                        ];
                    })
                    ->values()
                    ->toArray();
    }

    /**
     * Get activity statistics.
     */
    public function getStatisticsAttribute(): array
    {
        $activities = $this->activities();
        
        return [
            'total_activities' => $activities->count(),
            'completed_activities' => $activities->where('is_completed', true)->count(),
            'pending_activities' => $activities->where('is_completed', false)->count(),
            'total_points_awarded' => $activities->sum('points_earned'),
            'average_points' => $activities->avg('points_earned') ?? 0,
            'completion_rate' => $activities->count() > 0 ? 
                round(($activities->where('is_completed', true)->count() / $activities->count()) * 100, 2) : 0,
        ];
    }
}
