<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use MoonShine\JWT\Http\Middleware\AuthenticateApi;
use MoonShine\Laravel\Http\Middleware\Authenticate;
use Symfony\Component\HttpFoundation\Response;

class MoonShineAuthentication
{
    protected $webAuth;
    protected $apiAuth;

    public function __construct(Authenticate $webAuth, AuthenticateApi $apiAuth)
    {
        $this->webAuth = $webAuth;
        $this->apiAuth = $apiAuth;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip authentication for the authentication endpoint itself
        if ($request->is('*/authenticate') && $request->expectsJson()) {
            return $next($request);
        }
        
        // Use API authentication for JSON requests
        if ($request->expectsJson()) {
            return $this->apiAuth->handle($request, $next);
        }

        // Use web authentication for regular requests
        return $this->webAuth->handle($request, $next);
    }
}