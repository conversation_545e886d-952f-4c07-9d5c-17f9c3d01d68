<?php

namespace App\Console\Commands;

use App\Services\AssessmentService;
use App\Models\BookQuestion;
use App\Models\BookWord;
use App\Models\BookActivityType;
use App\Models\ProgramBookQuiz;
use App\Models\ProgramBookActivity;
use App\Models\Book;
use App\Models\Program;
use App\Models\User;
use Illuminate\Console\Command;

class TestAssessmentSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:assessment-system';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test that the assessment system is working correctly';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Testing Assessment System...');
        
        try {
            // Test models
            $this->testModels();
            
            // Test assessment service
            $this->testAssessmentService();
            
            // Test quiz functionality
            $this->testQuizFunctionality();
            
            // Test activity functionality
            $this->testActivityFunctionality();
            
            // Display statistics
            $this->displayStatistics();
            
            $this->info('');
            $this->info('🎉 All assessment system components are working correctly!');
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('❌ Error testing assessment system: ' . $e->getMessage());
            $this->error('   File: ' . $e->getFile() . ':' . $e->getLine());
            return Command::FAILURE;
        }
    }

    /**
     * Test model functionality.
     */
    private function testModels(): void
    {
        $this->info('Testing Models...');
        
        // Test BookQuestion model
        $questionCount = BookQuestion::count();
        $this->info("✅ BookQuestion model - {$questionCount} questions found");
        
        if ($questionCount > 0) {
            $question = BookQuestion::first();
            $this->info("   Sample question: {$question->question_text}");
            $this->info("   Answer options: " . count($question->answer_options));
            $this->info("   Difficulty: {$question->difficulty_level_name}");
            $this->info("   Page range: {$question->page_range}");
        }
        
        // Test BookWord model
        $wordCount = BookWord::count();
        $this->info("✅ BookWord model - {$wordCount} words found");
        
        if ($wordCount > 0) {
            $word = BookWord::first();
            $this->info("   Sample word: {$word->word_display}");
            $this->info("   Definition: " . ($word->definition ? 'Yes' : 'No'));
            $this->info("   Synonym/Antonym: " . ($word->synonym ? 'Yes' : 'No') . '/' . ($word->antonym ? 'Yes' : 'No'));
        }
        
        // Test BookActivityType model
        $activityTypeCount = BookActivityType::count();
        $this->info("✅ BookActivityType model - {$activityTypeCount} activity types found");
        
        if ($activityTypeCount > 0) {
            $activityType = BookActivityType::first();
            $this->info("   Sample activity: {$activityType->name} ({$activityType->category_name})");
            $this->info("   Base points: {$activityType->points_base}");
            $this->info("   Word count range: " . ($activityType->word_count_range ?? 'No limit'));
        }
        
        // Test quiz and activity models
        $quizCount = ProgramBookQuiz::count();
        $activityCount = ProgramBookActivity::count();
        $this->info("✅ ProgramBookQuiz model - {$quizCount} quizzes found");
        $this->info("✅ ProgramBookActivity model - {$activityCount} activities found");
    }

    /**
     * Test assessment service.
     */
    private function testAssessmentService(): void
    {
        $this->info('');
        $this->info('Testing AssessmentService...');
        
        $assessmentService = new AssessmentService();
        
        // Get test data
        $book = Book::first();
        $program = Program::where('is_active', true)->first();
        
        if (!$book || !$program) {
            $this->warn('No book or program found for testing service');
            return;
        }
        
        // Test book assessment readiness
        $readiness = $assessmentService->getBookAssessmentReadiness($book->id);
        $this->info("✅ Book assessment readiness checked");
        $this->info("   Questions available: {$readiness['questions']['total']}");
        $this->info("   Words available: {$readiness['words']['total']}");
        $this->info("   Ready for assessment: " . ($readiness['ready_for_assessment'] ? 'Yes' : 'No'));
        
        // Test statistics
        $stats = $assessmentService->getAssessmentStatistics($program->id);
        $this->info("✅ Assessment statistics generated");
        $this->info("   Total quizzes: {$stats['quizzes']['total']}");
        $this->info("   Total activities: {$stats['activities']['total_activities']}");
        
        // Test vocabulary question generation
        if ($readiness['words']['total'] > 0) {
            $vocabQuestions = $assessmentService->generateVocabularyQuestions($book->id, 3);
            $this->info("✅ Vocabulary questions generated: " . count($vocabQuestions));
        }
    }

    /**
     * Test quiz functionality.
     */
    private function testQuizFunctionality(): void
    {
        $this->info('');
        $this->info('Testing Quiz Functionality...');
        
        $quiz = ProgramBookQuiz::first();
        
        if (!$quiz) {
            $this->warn('No quiz found for testing');
            return;
        }
        
        $this->info("✅ Quiz found: {$quiz->quiz_type_name}");
        $this->info("   Total questions: {$quiz->total_questions}");
        $this->info("   Passing score: {$quiz->passing_score}%");
        $this->info("   Status: " . ($quiz->is_completed ? 'Completed' : 'Ongoing'));
        
        if ($quiz->is_completed) {
            $this->info("   Score: {$quiz->score_percentage}%");
            $this->info("   Passed: " . ($quiz->is_passed ? 'Yes' : 'No'));
        }
        
        // Test quiz questions
        $quizQuestions = $quiz->quizQuestions()->count();
        $this->info("   Quiz questions loaded: {$quizQuestions}");
        
        if ($quizQuestions > 0) {
            $firstQuestion = $quiz->quizQuestions()->first();
            $this->info("   Sample question order: {$firstQuestion->question_order}");
            $this->info("   Question answered: " . ($firstQuestion->is_answered ? 'Yes' : 'No'));
        }
    }

    /**
     * Test activity functionality.
     */
    private function testActivityFunctionality(): void
    {
        $this->info('');
        $this->info('Testing Activity Functionality...');
        
        $activity = ProgramBookActivity::first();
        
        if (!$activity) {
            $this->warn('No activity found for testing');
            return;
        }
        
        $this->info("✅ Activity found: {$activity->activityType->name}");
        $this->info("   Category: {$activity->activityType->category_name}");
        $this->info("   Student: {$activity->user->name}");
        $this->info("   Completed: " . ($activity->is_completed ? 'Yes' : 'No'));
        $this->info("   Reviewed: " . ($activity->is_reviewed ? 'Yes' : 'No'));
        
        if ($activity->is_completed) {
            $this->info("   Word count: " . ($activity->word_count ?? 'N/A'));
            $this->info("   Points earned: {$activity->points_earned}");
        }
        
        if ($activity->is_reviewed) {
            $this->info("   Reviewer: {$activity->reviewer->name}");
            $this->info("   Feedback: " . ($activity->feedback ? 'Yes' : 'No'));
        }
    }

    /**
     * Display comprehensive statistics.
     */
    private function displayStatistics(): void
    {
        $this->info('');
        $this->info('Assessment System Statistics:');
        
        // Content statistics
        $this->info('📚 Content:');
        $this->info("   Books with questions: " . Book::whereHas('questions')->count());
        $this->info("   Books with words: " . Book::whereHas('words')->count());
        $this->info("   Total questions: " . BookQuestion::count());
        $this->info("   Total vocabulary words: " . BookWord::count());
        $this->info("   Activity types: " . BookActivityType::count());
        
        // Assessment statistics
        $this->info('📝 Assessments:');
        $this->info("   Total quizzes: " . ProgramBookQuiz::count());
        $this->info("   Completed quizzes: " . ProgramBookQuiz::whereNotNull('completed_at')->count());
        $this->info("   Passed quizzes: " . ProgramBookQuiz::where('is_passed', true)->count());
        $this->info("   Total activities: " . ProgramBookActivity::count());
        $this->info("   Completed activities: " . ProgramBookActivity::where('is_completed', true)->count());
        $this->info("   Reviewed activities: " . ProgramBookActivity::whereNotNull('reviewed_by')->count());
        
        // Difficulty distribution
        $difficultyStats = [
            'easy' => BookQuestion::where('difficulty_level', 'easy')->count(),
            'medium' => BookQuestion::where('difficulty_level', 'medium')->count(),
            'hard' => BookQuestion::where('difficulty_level', 'hard')->count(),
        ];
        
        $this->info('📊 Question Difficulty Distribution:');
        foreach ($difficultyStats as $level => $count) {
            $this->info("   " . ucfirst($level) . ": {$count}");
        }
        
        // Quiz type distribution
        $quizTypeStats = [
            'completion' => ProgramBookQuiz::where('quiz_type', 'completion')->count(),
            'daily_reading' => ProgramBookQuiz::where('quiz_type', 'daily_reading')->count(),
            'practice' => ProgramBookQuiz::where('quiz_type', 'practice')->count(),
        ];
        
        $this->info('🎯 Quiz Type Distribution:');
        foreach ($quizTypeStats as $type => $count) {
            $this->info("   " . ucfirst(str_replace('_', ' ', $type)) . ": {$count}");
        }
    }
}
