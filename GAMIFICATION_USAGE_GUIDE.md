# Gamification System Usage Guide

## Accessing the Admin Panel

1. **Start the server**: `php artisan serve`
2. **Open browser**: Navigate to `http://localhost:8000/admin`
3. **Login**: Use your admin credentials

## Gamification Menu

In the MoonShine admin panel, you'll find a new **"Gamification"** menu group containing:

### 📚 Stories
- **Purpose**: Create and manage reading adventure stories
- **Features**: 
  - Configure story title, description, and cover image
  - Set up map grid dimensions (rows × columns)
  - Add background image for the story map
  - Activate/deactivate stories

### 📖 Story Chapters
- **Purpose**: Define sequential levels within each story
- **Features**:
  - Set chapter sequence and unlock rules
  - Define map coordinates for chapter positioning
  - Link chapters to specific stories
  - Configure unlock requirements

### 👤 Story Characters
- **Purpose**: Create characters that students can select
- **Features**:
  - Design character appearance and description
  - Set base character images
  - Activate/deactivate character availability
  - Link characters to specific stories

### ⭐ Character Stages
- **Purpose**: Define character progression and evolution
- **Features**:
  - Create multiple stages per character (<PERSON>pp<PERSON><PERSON> → Scholar → Master)
  - Set stage-specific images and abilities
  - Configure unlock rules for each stage
  - Define special abilities in JSON format

### 🏆 Story Achievements
- **Purpose**: Create rewards, badges, and collectibles
- **Achievement Types**:
  - **Item**: Collectible items
  - **Badge**: Recognition badges
  - **Reward**: Special rewards
  - **Trophy**: Competition trophies
  - **Collectible**: Rare collectibles
- **Features**:
  - Set map positioning (static or dynamic)
  - Configure unlock requirements
  - Design achievement images and descriptions

### ⚙️ Story Rules
- **Purpose**: Define unlock conditions for game elements
- **Rule Types**:
  1. **Points** (1): Require specific point amounts
  2. **Achievement Count** (2): Require number of achievements
  3. **Book Count** (3): Require number of books read
  4. **Specific Achievements** (4): Require particular achievements
  5. **Specific Books** (5): Require particular books
  6. **Chapter Completion** (6): Require chapter completion
  7. **Time Based** (7): Time-based requirements

### 📋 Story Rule Details
- **Purpose**: Define complex requirements for rules
- **Required Types**:
  1. **Achievement** (1): Specific achievement requirements
  2. **Book** (2): Specific book requirements
  3. **Chapter** (3): Specific chapter requirements
  4. **Character Stage** (4): Specific character stage requirements

## Sample Data

The system includes a complete sample story: **"The Magical Library Adventure"**

### Story Structure
```
The Magical Library Adventure (12×15 grid map)
├── Chapter 1: The Entrance Hall (unlocked by default)
├── Chapter 2: The Fiction Wing (requires 100 points)
├── Chapter 3: The Knowledge Tower (requires 3 books)
└── Chapter 4: The Secret Archive (requires 2 achievements)
```

### Characters Available
1. **Luna the Librarian** - A wise magical librarian
2. **Bookworm the Dragon** - A friendly book-loving dragon  
3. **Sage the Owl** - An ancient knowledge-keeper

Each character has 3 progression stages:
- **Stage 1**: Apprentice (basic abilities)
- **Stage 2**: Scholar (enhanced abilities)
- **Stage 3**: Master (full abilities)

### Achievements to Earn
1. **First Book Badge** - For reading your first book
2. **Reading Streak Trophy** - For consistent reading habits
3. **Knowledge Crystal** - For mastering comprehension (dynamic position)
4. **Master Reader Crown** - Ultimate achievement for completion

## Creating Your Own Story

### Step 1: Create a Story
1. Go to **Gamification → Stories**
2. Click **Create**
3. Fill in:
   - Title and description
   - Cover image path
   - Map grid dimensions (e.g., 10×10)
   - Background image (optional)
   - Set as active

### Step 2: Define Rules
1. Go to **Gamification → Story Rules**
2. Create rules for different unlock conditions:
   - Points-based rules (e.g., 50 points)
   - Book count rules (e.g., 2 books)
   - Achievement count rules (e.g., 1 achievement)

### Step 3: Create Chapters
1. Go to **Gamification → Story Chapters**
2. Create sequential chapters:
   - Set sequence numbers (1, 2, 3, ...)
   - Assign unlock rules (except first chapter)
   - Set map coordinates for positioning

### Step 4: Design Characters
1. Go to **Gamification → Story Characters**
2. Create characters with:
   - Descriptive names and backgrounds
   - Base character images
   - Active status

### Step 5: Add Character Stages
1. Go to **Gamification → Character Stages**
2. For each character, create stages:
   - Stage 1: Always unlocked
   - Stage 2+: Assign unlock rules
   - Define abilities in JSON format

### Step 6: Create Achievements
1. Go to **Gamification → Story Achievements**
2. Design various achievement types:
   - Set appropriate unlock rules
   - Position on map coordinates
   - Choose static or dynamic positioning

## Best Practices

### Map Design
- Use reasonable grid sizes (8×8 to 15×15)
- Plan chapter progression across the map
- Leave space for achievements and character positioning

### Rule Configuration
- Start with simple rules (points, book counts)
- Use complex rules (specific items) for advanced unlocks
- Create progressive difficulty

### Character Progression
- Design meaningful stage progressions
- Use descriptive stage names
- Define useful abilities for each stage

### Achievement Design
- Mix different achievement types
- Use dynamic positioning for special items
- Create both easy and challenging achievements

## Troubleshooting

### Common Issues
1. **Icon not found**: Ensure all icons use valid Heroicon names
2. **Rule not working**: Check rule type matches the requirement
3. **Character not showing**: Verify character is set to active
4. **Map positioning**: Ensure coordinates are within grid bounds

### Validation Errors
- All required fields must be filled
- Sequence numbers must be unique per story
- Stage numbers must be unique per character
- Map coordinates must be non-negative

## Next Steps

After setting up your gamification system:

1. **API Integration**: Create API endpoints for mobile/web apps
2. **User Progress**: Add tables to track student progress
3. **Frontend**: Build interactive map and character selection
4. **Analytics**: Track engagement and completion rates
5. **Notifications**: Add achievement unlock notifications

The gamification system is now ready to engage students in their reading journey through interactive storytelling and progression mechanics!
