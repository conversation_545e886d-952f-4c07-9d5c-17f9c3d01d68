<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class StoryRule extends BaseModel
{
    /**
     * Rule type constants.
     */
    const TYPE_POINTS = 1;
    const TYPE_ACHIEVEMENT_COUNT = 2;
    const TYPE_BOOK_COUNT = 3;
    const TYPE_SPECIFIC_ACHIEVEMENTS = 4;
    const TYPE_SPECIFIC_BOOKS = 5;
    const TYPE_CHAPTER_COMPLETION = 6;
    const TYPE_TIME_BASED = 7;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'story_id',
        'rule_type',
        'quantity',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'rule_type' => 'integer',
            'quantity' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get all rule types.
     */
    public static function getRuleTypes(): array
    {
        return [
            self::TYPE_POINTS => 'Points',
            self::TYPE_ACHIEVEMENT_COUNT => 'Achievement Count',
            self::TYPE_BOOK_COUNT => 'Book Count',
            self::TYPE_SPECIFIC_ACHIEVEMENTS => 'Specific Achievements',
            self::TYPE_SPECIFIC_BOOKS => 'Specific Books',
            self::TYPE_CHAPTER_COMPLETION => 'Chapter Completion',
            self::TYPE_TIME_BASED => 'Time Based',
        ];
    }

    /**
     * Get rule type name.
     */
    public function getRuleTypeNameAttribute(): string
    {
        return self::getRuleTypes()[$this->rule_type] ?? 'Unknown';
    }

    /**
     * Get the story this rule belongs to.
     */
    public function story(): BelongsTo
    {
        return $this->belongsTo(Story::class);
    }

    /**
     * Get the rule details for this rule.
     */
    public function details(): HasMany
    {
        return $this->hasMany(StoryRuleDetail::class, 'rule_id');
    }

    /**
     * Get chapters that use this rule.
     */
    public function chapters(): HasMany
    {
        return $this->hasMany(StoryChapter::class, 'unlock_rule_id');
    }

    /**
     * Get character stages that use this rule.
     */
    public function characterStages(): HasMany
    {
        return $this->hasMany(StoryCharacterStage::class, 'unlock_rule_id');
    }

    /**
     * Get achievements that use this rule.
     */
    public function achievements(): HasMany
    {
        return $this->hasMany(StoryAchievement::class, 'unlock_rule_id');
    }

    /**
     * Check if this rule requires specific items.
     */
    public function requiresSpecificItems(): bool
    {
        return in_array($this->rule_type, [
            self::TYPE_SPECIFIC_ACHIEVEMENTS,
            self::TYPE_SPECIFIC_BOOKS,
        ]);
    }

    /**
     * Check if this rule requires quantity.
     */
    public function requiresQuantity(): bool
    {
        return in_array($this->rule_type, [
            self::TYPE_POINTS,
            self::TYPE_ACHIEVEMENT_COUNT,
            self::TYPE_BOOK_COUNT,
        ]);
    }

    /**
     * Get formatted rule description.
     */
    public function getDescriptionAttribute(): string
    {
        $description = $this->rule_type_name;
        
        if ($this->requiresQuantity()) {
            $description .= ': ' . $this->quantity;
        }
        
        if ($this->requiresSpecificItems() && $this->details->count() > 0) {
            $description .= ' (' . $this->details->count() . ' specific items)';
        }
        
        return $description;
    }

    /**
     * Scope to filter by rule type.
     */
    public function scopeOfType($query, int $type)
    {
        return $query->where('rule_type', $type);
    }
}
