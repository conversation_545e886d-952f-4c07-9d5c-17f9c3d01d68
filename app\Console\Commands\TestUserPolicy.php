<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;
use App\Policies\UserPolicy;
use Illuminate\Support\Facades\Auth;

class TestUserPolicy extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:user-policy';

    /**
     * The console command description.
     */
    protected $description = 'Test UserPolicy authorization';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== UserPolicy Authorization Test ===');
        $this->newLine();

        $policy = new UserPolicy();

        // Test System Admin
        $systemAdmin = User::whereHas('role', function($q) {
            $q->where('level', Role::LEVEL_SYSTEM_ADMIN);
        })->first();

        if ($systemAdmin) {
            $this->info("System Admin ({$systemAdmin->name}):");
            $this->line("  - Can view any: " . ($policy->viewAny($systemAdmin) ? 'YES' : 'NO'));
            $this->line("  - Can create: " . ($policy->create($systemAdmin) ? 'YES' : 'NO'));
            
            // Test viewing a student
            $student = User::whereHas('role', function($q) {
                $q->where('level', Role::LEVEL_STUDENT);
            })->first();
            
            if ($student) {
                $this->line("  - Can view student: " . ($policy->view($systemAdmin, $student) ? 'YES' : 'NO'));
                $this->line("  - Can update student: " . ($policy->update($systemAdmin, $student) ? 'YES' : 'NO'));
                $this->line("  - Can delete student: " . ($policy->delete($systemAdmin, $student) ? 'YES' : 'NO'));
            }
        }

        $this->newLine();

        // Test Teacher
        $teacher = User::whereHas('role', function($q) {
            $q->where('level', Role::LEVEL_TEACHER);
        })->first();

        if ($teacher) {
            $this->info("Teacher ({$teacher->name}):");
            $this->line("  - Can view any: " . ($policy->viewAny($teacher) ? 'YES' : 'NO'));
            $this->line("  - Can create: " . ($policy->create($teacher) ? 'YES' : 'NO'));
            
            // Test viewing a student
            $student = User::whereHas('role', function($q) {
                $q->where('level', Role::LEVEL_STUDENT);
            })->first();
            
            if ($student) {
                $this->line("  - Can view student: " . ($policy->view($teacher, $student) ? 'YES' : 'NO'));
                $this->line("  - Can update student: " . ($policy->update($teacher, $student) ? 'YES' : 'NO'));
                $this->line("  - Can delete student: " . ($policy->delete($teacher, $student) ? 'YES' : 'NO'));
            }
        }

        $this->newLine();

        // Test Student
        $student = User::whereHas('role', function($q) {
            $q->where('level', Role::LEVEL_STUDENT);
        })->first();

        if ($student) {
            $this->info("Student ({$student->name}):");
            $this->line("  - Can view any: " . ($policy->viewAny($student) ? 'YES' : 'NO'));
            $this->line("  - Can create: " . ($policy->create($student) ? 'YES' : 'NO'));
        }

        $this->newLine();
        $this->info('=== Test Complete ===');
        
        return 0;
    }
}
