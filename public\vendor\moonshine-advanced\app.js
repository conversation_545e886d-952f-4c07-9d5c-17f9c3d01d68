document.addEventListener("moonshine:init",()=>{MoonShine.onCallback("spaMenu",function(e,i,s){const t=s.$el,n=t.closest(".menu"),a=t.closest("li");n.querySelectorAll("li").forEach(h=>{h.classList.remove("_is-active"),h.querySelector("a").removeAttribute("data-stop-async")}),a.classList.add("_is-active"),t.setAttribute("data-stop-async",!0);const c=new URL(t.href);c.searchParams.delete("_fragment-load"),history.pushState({},"",c.href)}),MoonShine.onCallback("asyncTabs",function(e,i,s){const t=s.$el;t.closest(".async-tabs-container").querySelectorAll("a").forEach(a=>{a.classList.remove("_is-active"),a.removeAttribute("data-stop-async")}),t.classList.add("_is-active"),t.setAttribute("data-stop-async",!0)})});document.addEventListener("alpine:init",()=>{Alpine.data("asyncTabs",()=>({init(){const e=this;this.$nextTick(()=>{e.$root.querySelector(".async-tabs-container a").click()})}})),Alpine.data("stepper",()=>({head:null,steps:[],heads:[],active:1,activeHead:1,container:1,activeStep:1,finishBlock:1,finished:!1,lock:!1,lockWhenFinish:!1,loaded:[],init(){this.lock=this.$root.dataset.lock,this.lockWhenFinish=this.$root.dataset.lockWhenFinish,this.active=parseInt(this.$root.dataset.current??this.active),this.head=this.$root.querySelector(".js-stepper-head-container"),this.container=this.$root.querySelector(".js-stepper-content-container"),this.finishBlock=this.$root.querySelector(".js-stepper-finish-content"),this.heads=this.head.querySelectorAll(".js-stepper-head"),this.steps=this.container.querySelectorAll(".js-stepper-content"),this.active>this.steps.length?this.finish():this._change()},current(e){this.lock||this.lockWhenFinish&&this.finished||(this.active=e,this._change())},next(){this.active++,this._change()},prev(){this.active--,this._change()},finish(){this.finished=!0,this.active++,this._change(),this.finishBlock.style.display="block"},_change(){if(this.finishBlock.style.display="none",this.activeHead=this.head.querySelector(`.js-stepper-head-${this.active}`),this.activeStep=this.container.querySelector(`.js-stepper-content-${this.active}`),this.activeHead){const e=`.js-stepper-content-${this.active} .js-stepper-content-html`;if(this.activeHead.dataset.asyncUrl&&this.loaded[this.active]===void 0){let i=function(s,t){t.loading=!1,t.loaded[t.active]=!0};MoonShine.request(this,this.activeHead.dataset.asyncUrl,"get",{},{},{events:this.activeHead.dataset.asyncEvents,selector:e,beforeHandleResponse:i,errorCallback:i})}this.activeHead.dataset.asyncFinishEvents&&MoonShine.dispatchEvents(this.activeHead.dataset.asyncFinishEvents,"",this)}this.steps.forEach(e=>e.style.display="none"),this.heads.forEach((e,i)=>{e.classList.remove("active");let s=e.querySelector(".js-stepper-head-state-default");e.querySelector(".js-stepper-head-state-active").style.display="none",s.style.display="block",s.classList.remove("js-stepper-head-state-done"),i<this.active&&s.classList.add("js-stepper-head-state-done")}),this.activeHead&&this.activeStep&&(this.activeStep.style.display="block",this.activeHead.classList.add("active"),this.activeHead.querySelector(".js-stepper-head-state-active").style.display="block",this.activeHead.querySelector(".js-stepper-head-state-default").style.display="none")}}))});
