<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update role levels to remove the gap left by Group School Admin
        // Old levels: 1=System Admin, 2=Group School Admin, 3=School Admin, 4=Teacher, 5=Student
        // New levels: 1=System Admin, 2=School Admin, 3=Teacher, 4=Student
        
        // First, update users who had Group School Admin role to School Admin
        $groupSchoolAdminRoleId = DB::table('roles')->where('level', 2)->value('id');
        $schoolAdminRoleId = DB::table('roles')->where('level', 3)->value('id');

        if ($groupSchoolAdminRoleId && $schoolAdminRoleId) {
            DB::table('users')
                ->where('role_id', $groupSchoolAdminRoleId)
                ->update(['role_id' => $schoolAdminRoleId]);
        }
        
        // Update role levels
        DB::table('roles')->where('level', 3)->update(['level' => 2]); // School Admin: 3 -> 2
        DB::table('roles')->where('level', 4)->update(['level' => 3]); // Teacher: 4 -> 3
        DB::table('roles')->where('level', 5)->update(['level' => 4]); // Student: 5 -> 4
        
        // Delete the Group School Admin role
        DB::table('roles')->where('level', 2)->where('name', 'LIKE', '%Group School%')->delete();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This is a destructive migration, so we'll just recreate the role structure
        // but we can't restore the original user assignments
        
        // Shift levels back up to make room for Group School Admin
        DB::table('roles')->where('level', 4)->update(['level' => 5]); // Student: 4 -> 5
        DB::table('roles')->where('level', 3)->update(['level' => 4]); // Teacher: 3 -> 4
        DB::table('roles')->where('level', 2)->update(['level' => 3]); // School Admin: 2 -> 3
        
        // Recreate Group School Admin role
        DB::table('roles')->insert([
            'name' => 'Group School Administrator',
            'description' => 'Manages multiple schools within a group',
            'level' => 2,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
};
