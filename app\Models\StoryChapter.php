<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StoryChapter extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'story_id',
        'title',
        'description',
        'sequence',
        'unlock_rule_id',
        'map_start_x',
        'map_start_y',
        'map_end_x',
        'map_end_y',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'sequence' => 'integer',
            'map_start_x' => 'integer',
            'map_start_y' => 'integer',
            'map_end_x' => 'integer',
            'map_end_y' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get the story this chapter belongs to.
     */
    public function story(): BelongsTo
    {
        return $this->belongsTo(Story::class);
    }

    /**
     * Get the unlock rule for this chapter.
     */
    public function unlockRule(): BelongsTo
    {
        return $this->belongsTo(StoryRule::class, 'unlock_rule_id');
    }

    /**
     * Check if chapter has unlock rule.
     */
    public function hasUnlockRule(): bool
    {
        return !is_null($this->unlock_rule_id);
    }

    /**
     * Check if chapter has map coordinates.
     */
    public function hasMapCoordinates(): bool
    {
        return !is_null($this->map_start_x) && !is_null($this->map_start_y);
    }

    /**
     * Get formatted chapter name with sequence.
     */
    public function getFullTitleAttribute(): string
    {
        return 'Chapter ' . $this->sequence . ': ' . $this->title;
    }

    /**
     * Get start coordinates as array.
     */
    public function getStartCoordinatesAttribute(): ?array
    {
        if (!$this->hasMapCoordinates()) {
            return null;
        }
        
        return [
            'x' => $this->map_start_x,
            'y' => $this->map_start_y,
        ];
    }

    /**
     * Get end coordinates as array.
     */
    public function getEndCoordinatesAttribute(): ?array
    {
        if (is_null($this->map_end_x) || is_null($this->map_end_y)) {
            return null;
        }
        
        return [
            'x' => $this->map_end_x,
            'y' => $this->map_end_y,
        ];
    }

    /**
     * Get all coordinates as array.
     */
    public function getCoordinatesAttribute(): array
    {
        return [
            'start' => $this->start_coordinates,
            'end' => $this->end_coordinates,
        ];
    }

    /**
     * Scope to order by sequence.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sequence');
    }

    /**
     * Scope to get chapters for a specific story.
     */
    public function scopeForStory($query, int $storyId)
    {
        return $query->where('story_id', $storyId);
    }

    /**
     * Scope to get chapters with unlock rules.
     */
    public function scopeWithUnlockRules($query)
    {
        return $query->whereNotNull('unlock_rule_id');
    }

    /**
     * Scope to get chapters without unlock rules.
     */
    public function scopeWithoutUnlockRules($query)
    {
        return $query->whereNull('unlock_rule_id');
    }

    /**
     * Get the next chapter in sequence.
     */
    public function getNextChapterAttribute(): ?StoryChapter
    {
        return static::where('story_id', $this->story_id)
                    ->where('sequence', '>', $this->sequence)
                    ->orderBy('sequence')
                    ->first();
    }

    /**
     * Get the previous chapter in sequence.
     */
    public function getPreviousChapterAttribute(): ?StoryChapter
    {
        return static::where('story_id', $this->story_id)
                    ->where('sequence', '<', $this->sequence)
                    ->orderBy('sequence', 'desc')
                    ->first();
    }

    /**
     * Check if this is the first chapter.
     */
    public function isFirst(): bool
    {
        return $this->sequence === 1;
    }

    /**
     * Check if this is the last chapter.
     */
    public function isLast(): bool
    {
        return is_null($this->next_chapter);
    }
}
