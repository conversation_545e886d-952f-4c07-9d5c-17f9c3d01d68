<?php

declare(strict_types=1);

namespace App\MoonShine\Layouts;

use MoonShine\Laravel\Layouts\AppLayout;
use MoonShine\ColorManager\ColorManager;
use MoonShine\Contracts\ColorManager\ColorManagerContract;
use MoonShine\MenuManager\MenuGroup;
use MoonShine\MenuManager\MenuItem;

// Import all resource classes
use App\MoonShine\Resources\RoleResource;
use App\MoonShine\Resources\UserResource;
use App\MoonShine\Resources\TermResource;
use App\MoonShine\Resources\OrganizationResource;
use App\MoonShine\Resources\GradeLevelResource;
use App\MoonShine\Resources\SchoolClassResource;
use App\MoonShine\Resources\TermUserResource;
use App\MoonShine\Resources\AuthorResource;
use App\MoonShine\Resources\PublisherResource;
use App\MoonShine\Resources\BookResource;
use App\MoonShine\Resources\StoryResource;
use App\MoonShine\Resources\StoryRuleResource;
use App\MoonShine\Resources\StoryRuleDetailResource;
use App\MoonShine\Resources\StoryChapterResource;
use App\MoonShine\Resources\StoryCharacterResource;
use App\MoonShine\Resources\StoryCharacterStageResource;
use App\MoonShine\Resources\StoryAchievementResource;
use App\MoonShine\Resources\StoryBookResource;
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\ProgramTeamResource;
use App\MoonShine\Resources\ProgramUserLevelResource;
use App\MoonShine\Resources\ProgramUserPointResource;
use App\MoonShine\Resources\ProgramUserBookResource;
use App\MoonShine\Resources\ProgramUserAchievementResource;
use App\MoonShine\Resources\ProgramSchoolResource;
use App\MoonShine\Resources\ProgramClassResource;
use App\MoonShine\Resources\ProgramBookResource;
use App\MoonShine\Resources\ProgramTeamMemberResource;
use App\MoonShine\Resources\ProgramUserCharacterResource;
use App\MoonShine\Resources\ProgramUserMapResource;
use App\MoonShine\Resources\ProgramTaskResource;
use App\MoonShine\Resources\ProgramTaskInstanceResource;
use App\MoonShine\Resources\ProgramTaskActionResource;
use App\MoonShine\Resources\BookQuestionResource;
use App\MoonShine\Resources\BookWordResource;
use App\MoonShine\Resources\BookActivityTypeResource;
use App\MoonShine\Resources\ProgramBookQuizResource;
use App\MoonShine\Resources\ProgramBookActivityResource;
use App\MoonShine\Resources\ProgramReadingLogResource;
use App\MoonShine\Resources\UserAgreementResource;
use MoonShine\UI\Components\Layout\Layout;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Sweet1s\MoonshineRBAC\Components\MenuRBAC;
use Sweet1s\MoonshineRBAC\Resource\PermissionResource;

final class MoonShineLayout extends AppLayout
{
    protected function assets(): array
    {
        return [
            ...parent::assets(),
        ];
    }

    protected function menu(): array
    {
        return MenuRBAC::menu(
             MenuGroup::make(__('admin.system'), [
                MenuItem::make(__('admin.roles'), RoleResource::class),
                MenuItem::make('permissions', PermissionResource::class)
                    ->translatable('moonshine-rbac::ui')
                    ->icon('s.shield-check'),
                
                MenuItem::make(__('admin.terms'), TermResource::class),
                MenuItem::make(__('admin.grade_levels'), GradeLevelResource::class),
                MenuItem::make(__('admin.organizations'), OrganizationResource::class),
                MenuItem::make(__('admin.term_users'), TermUserResource::class),
                MenuItem::make(__('admin.user_agreements'), UserAgreementResource::class),
             ]),
            MenuItem::make(__('admin.school_classes'), SchoolClassResource::class),
            MenuItem::make(__('admin.users'), UserResource::class),
            MenuGroup::make(__('admin.books'), [
                MenuItem::make(__('admin.authors'), AuthorResource::class),
                MenuItem::make(__('admin.publishers'), PublisherResource::class),
                MenuItem::make(__('admin.books'), BookResource::class),
            ]),
            MenuGroup::make(__('admin.gamification'), [
                MenuItem::make(__('admin.stories'), StoryResource::class),
                MenuItem::make(__('admin.story_books'), StoryBookResource::class),
                MenuItem::make(__('admin.story_chapters'), StoryChapterResource::class),
                MenuItem::make(__('admin.story_characters'), StoryCharacterResource::class),
                MenuItem::make(__('admin.story_character_stages'), StoryCharacterStageResource::class),
                MenuItem::make(__('admin.story_achievements'), StoryAchievementResource::class),
                MenuItem::make(__('admin.story_rules'), StoryRuleResource::class),
                MenuItem::make(__('admin.story_rule_details'), StoryRuleDetailResource::class),
            ]),
            MenuGroup::make(__('admin.reading_programs'), [
                MenuItem::make(__('admin.programs'), ProgramResource::class),
                MenuItem::make(__('admin.program_schools'), ProgramSchoolResource::class),
                MenuItem::make(__('admin.program_classes'), ProgramClassResource::class),
                MenuItem::make(__('admin.program_books'), ProgramBookResource::class),
                MenuItem::make(__('admin.program_teams'), ProgramTeamResource::class),
                MenuItem::make(__('admin.program_team_members'), ProgramTeamMemberResource::class),
                MenuItem::make(__('admin.program_user_levels'), ProgramUserLevelResource::class),
                MenuItem::make(__('admin.program_user_achievements'), ProgramUserAchievementResource::class),
                MenuItem::make(__('admin.program_user_characters'), ProgramUserCharacterResource::class),
                MenuItem::make(__('admin.program_user_maps'), ProgramUserMapResource::class),
                MenuItem::make(__('admin.program_user_points'), ProgramUserPointResource::class),
                MenuItem::make(__('admin.program_user_books'), ProgramUserBookResource::class),
                MenuItem::make(__('admin.program_tasks'), ProgramTaskResource::class),
                MenuItem::make(__('admin.program_task_instances'), ProgramTaskInstanceResource::class),
                MenuItem::make(__('admin.program_task_actions'), ProgramTaskActionResource::class),
            ]),
             MenuGroup::make(__('admin.assessment_system'), [
                MenuItem::make(__('admin.book_questions'), BookQuestionResource::class),
                MenuItem::make(__('admin.book_words'), BookWordResource::class),
                MenuItem::make(__('admin.book_activity_types'), BookActivityTypeResource::class),
                MenuItem::make(__('admin.program_book_quizzes'), ProgramBookQuizResource::class),
                MenuItem::make(__('admin.program_book_activities'), ProgramBookActivityResource::class),
             ]),
             MenuGroup::make(__('admin.reading_log_system'), [
                MenuItem::make(__('admin.program_reading_logs'), ProgramReadingLogResource::class),
             ]),
        );            
    }

    /**
     * @param ColorManager $colorManager
     */
    protected function colors(ColorManagerContract $colorManager): void
    {
        parent::colors($colorManager);

        // $colorManager->primary('#00000');
    }

    public function build(): Layout
    {
        return parent::build();
    }
}
