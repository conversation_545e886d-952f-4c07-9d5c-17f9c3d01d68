<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\BookQuestion;
use App\Models\Book;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Fields\Url;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;

// Import related resources
use App\MoonShine\Resources\BookResource;

/**
 * @extends BaseResource<BookQuestion>
 */
#[Icon('question-mark-circle')]
class BookQuestionResource extends BaseResource
{
    protected string $model = BookQuestion::class;

    protected array $with = ['book', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.book_questions');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name)
                ->sortable(),
            
            Text::make(__('admin.question_text'), 'question_text')
                ->sortable(),
            
            Select::make(__('admin.difficulty_level'), 'difficulty_level')
                ->options(BookQuestion::getDifficultyLevels())
                ->sortable(),
            
            Text::make(__('admin.page_range'), 'page_range')
                ->badge('blue'),
            
            Switcher::make(__('admin.is_active'), 'is_active')
                ->sortable(),
            
            Text::make(__('admin.media_type'), 'media_type')
                ->badge(fn($mediaType) => match($mediaType) {
                    'video' => 'red',
                    'audio' => 'yellow',
                    'image' => 'green',
                    default => 'gray'
                }),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        BelongsTo::make(__('admin.books'), 'book', 
                            formatted: fn(Book $book) => $book->name,
                            resource: BookResource::class)
                            ->required()
                            ->placeholder(__('admin.select_book')),
                        
                        Textarea::make(__('admin.question_text'), 'question_text')
                            ->required()
                            ->placeholder(__('admin.enter_question_text')),
                        
                        Text::make(__('admin.correct_answer'), 'correct_answer')
                            ->required()
                            ->placeholder(__('admin.enter_correct_answer')),
                        
                        Flex::make([
                            Select::make(__('admin.difficulty_level'), 'difficulty_level')
                                ->options(BookQuestion::getDifficultyLevels())
                                ->required()
                                ->default('medium'),
                            
                            Switcher::make(__('admin.is_active'), 'is_active')
                                ->default(true),
                        ]),
                    ]),
                    
                    Tab::make(__('admin.answer_options'), [
                        Text::make(__('admin.incorrect_answer') . ' 1', 'incorrect_answer_1')
                            ->placeholder(__('admin.enter_incorrect_answer')),
                        
                        Text::make(__('admin.incorrect_answer') . ' 2', 'incorrect_answer_2')
                            ->placeholder(__('admin.enter_incorrect_answer')),
                        
                        Text::make(__('admin.incorrect_answer') . ' 3', 'incorrect_answer_3')
                            ->placeholder(__('admin.enter_incorrect_answer')),
                        
                        Text::make(__('admin.incorrect_answer') . ' 4', 'incorrect_answer_4')
                            ->placeholder(__('admin.enter_incorrect_answer')),
                        
                        Text::make(__('admin.incorrect_answer') . ' 5', 'incorrect_answer_5')
                            ->placeholder(__('admin.enter_incorrect_answer')),
                    ]),
                    
                    Tab::make(__('admin.page_reference'), [
                        Flex::make([
                            Number::make(__('admin.page_start'), 'page_start')
                                ->min(1)
                                ->placeholder(__('admin.enter_page_start')),
                            
                            Number::make(__('admin.page_end'), 'page_end')
                                ->min(1)
                                ->placeholder(__('admin.enter_page_end')),
                        ]),
                    ]),
                    
                    Tab::make(__('admin.media_content'), [
                        Url::make(__('admin.question_image_url'), 'question_image_url')
                            ->placeholder(__('admin.enter_image_url')),
                        
                        Url::make(__('admin.question_audio_url'), 'question_audio_url')
                            ->placeholder(__('admin.enter_audio_url')),
                        
                        Url::make(__('admin.question_video_url'), 'question_video_url')
                            ->placeholder(__('admin.enter_video_url')),
                    ]),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.question_text'), 'question_text'),
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name),
            Text::make(__('admin.correct_answer'), 'correct_answer')
                ->badge('green'),
            Text::make(__('admin.incorrect_answer') . ' 1', 'incorrect_answer_1')
                ->badge('red'),
            Text::make(__('admin.incorrect_answer') . ' 2', 'incorrect_answer_2')
                ->badge('red'),
            Text::make(__('admin.incorrect_answer') . ' 3', 'incorrect_answer_3')
                ->badge('red'),
            Text::make(__('admin.incorrect_answer') . ' 4', 'incorrect_answer_4')
                ->badge('red'),
            Text::make(__('admin.incorrect_answer') . ' 5', 'incorrect_answer_5')
                ->badge('red'),
            Text::make(__('admin.difficulty_level'), 'difficulty_level_name')
                ->badge('purple'),
            Text::make(__('admin.page_range'), 'page_range')
                ->badge('blue'),
            Switcher::make(__('admin.is_active'), 'is_active')
                ->disabled(),
            Url::make(__('admin.question_image_url'), 'question_image_url'),
            Url::make(__('admin.question_audio_url'), 'question_audio_url'),
            Url::make(__('admin.question_video_url'), 'question_video_url'),
            Text::make(__('admin.media_type'), 'media_type')
                ->badge(fn($mediaType) => match($mediaType) {
                    'video' => 'red',
                    'audio' => 'yellow',
                    'image' => 'green',
                    default => 'gray'
                }),
            ...parent::getCommonDetailFields(),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name,
                resource: BookResource::class),
            Select::make(__('admin.difficulty_level'), 'difficulty_level')
                ->options(BookQuestion::getDifficultyLevels()),
            Switcher::make(__('admin.is_active'), 'is_active'),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'book_id' => ['required', 'exists:books,id'],
            'question_text' => ['required', 'string'],
            'correct_answer' => ['required', 'string', 'max:255'],
            'incorrect_answer_1' => ['nullable', 'string', 'max:255'],
            'incorrect_answer_2' => ['nullable', 'string', 'max:255'],
            'incorrect_answer_3' => ['nullable', 'string', 'max:255'],
            'incorrect_answer_4' => ['nullable', 'string', 'max:255'],
            'incorrect_answer_5' => ['nullable', 'string', 'max:255'],
            'page_start' => ['nullable', 'integer', 'min:1'],
            'page_end' => ['nullable', 'integer', 'min:1', 'gte:page_start'],
            'difficulty_level' => ['required', 'in:easy,medium,hard'],
            'is_active' => ['boolean'],
            'question_image_url' => ['nullable', 'url'],
            'question_audio_url' => ['nullable', 'url'],
            'question_video_url' => ['nullable', 'url'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['question_text', 'correct_answer'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }
}
