<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('program_book_activities', function (Blueprint $table) {
            $table->id();
            
            // Core activity information
            $table->foreignId('program_id')->constrained('programs')->onDelete('cascade');
            $table->foreignId('book_id')->constrained('books')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('activity_type_id')->constrained('book_activity_types')->onDelete('cascade');
            
            // Task integration (optional)
            $table->foreignId('program_task_instance_id')->nullable()
                  ->constrained('program_task_instances')->onDelete('set null')
                  ->comment('If assigned as task');
            
            // Activity content and metrics
            $table->text('activity_content')->nullable()->comment('Student\'s work/response');
            $table->integer('word_count')->nullable()->comment('For writing activities');
            $table->integer('points_earned')->default(0)->comment('Points awarded');
            
            // Status tracking
            $table->boolean('is_completed')->default(false)->comment('Completion status');
            $table->datetime('completed_at')->nullable()->comment('When activity was completed');
            
            // Review information
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null')
                  ->comment('Teacher who reviewed');
            $table->datetime('reviewed_at')->nullable()->comment('When activity was reviewed');
            $table->text('feedback')->nullable()->comment('Teacher feedback');
            
            // Audit fields
            $table->timestamps();
            $table->softDeletes();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Indexes for performance
            $table->index(['program_id', 'book_id', 'user_id'], 'program_book_user_idx');
            $table->index(['user_id', 'activity_type_id'], 'user_activity_type_idx');
            $table->index(['book_id', 'activity_type_id'], 'book_activity_type_idx');
            $table->index(['is_completed', 'completed_at'], 'completed_status_idx');
            $table->index(['reviewed_by', 'reviewed_at'], 'review_status_idx');
            $table->index('program_task_instance_id');
            $table->index('points_earned');

            // Composite index for student activity tracking
            $table->index(['user_id', 'book_id', 'activity_type_id'], 'user_book_activity_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('program_book_activities');
    }
};
