<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class ProgramTask extends BaseModel
{
    use SoftDeletes;

    /**
     * Task type constants.
     */
    const TYPE_READING_LOG = 'reading_log';
    const TYPE_ACTIVITY = 'activity';
    const TYPE_QUESTION = 'question';
    const TYPE_PHYSICAL = 'physical';

    /**
     * Recurrence pattern constants.
     */
    const RECURRENCE_DAILY = 'daily';
    const RECURRENCE_WEEKLY = 'weekly';

    /**
     * The table associated with the model.
     */
    protected $table = 'program_tasks';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_id',
        'name',
        'description',
        'task_type',
        'is_recurring',
        'recurrence_pattern',
        'start_date',
        'end_date',
        'points',
        'book_id',
        'page_start',
        'page_end',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'is_recurring' => 'boolean',
            'start_date' => 'date',
            'end_date' => 'date',
            'points' => 'integer',
            'page_start' => 'integer',
            'page_end' => 'integer',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get all task types.
     */
    public static function getTaskTypes(): array
    {
        return [
            self::TYPE_READING_LOG => 'Reading Log',
            self::TYPE_ACTIVITY => 'Activity',
            self::TYPE_QUESTION => 'Question',
            self::TYPE_PHYSICAL => 'Physical',
        ];
    }

    /**
     * Get all recurrence patterns.
     */
    public static function getRecurrencePatterns(): array
    {
        return [
            self::RECURRENCE_DAILY => 'Daily',
            self::RECURRENCE_WEEKLY => 'Weekly',
        ];
    }

    /**
     * Get task type name.
     */
    public function getTaskTypeNameAttribute(): string
    {
        return self::getTaskTypes()[$this->task_type] ?? 'Unknown';
    }

    /**
     * Get recurrence pattern name.
     */
    public function getRecurrencePatternNameAttribute(): string
    {
        return $this->recurrence_pattern ? 
               (self::getRecurrencePatterns()[$this->recurrence_pattern] ?? 'Unknown') : 
               'One-time';
    }

    /**
     * Get the program this task belongs to.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the book associated with this task (optional).
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the task instances for this task.
     */
    public function instances(): HasMany
    {
        return $this->hasMany(ProgramTaskInstance::class);
    }

    /**
     * Scope to get active tasks (within date range).
     */
    public function scopeActive($query)
    {
        $now = now()->toDateString();
        return $query->where('start_date', '<=', $now)
                    ->where('end_date', '>=', $now);
    }

    /**
     * Scope to get recurring tasks.
     */
    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    /**
     * Scope to get one-time tasks.
     */
    public function scopeOneTime($query)
    {
        return $query->where('is_recurring', false);
    }

    /**
     * Scope to filter by task type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('task_type', $type);
    }

    /**
     * Scope to filter by program.
     */
    public function scopeForProgram($query, int $programId)
    {
        return $query->where('program_id', $programId);
    }

    /**
     * Check if task is currently active.
     */
    public function getIsActiveAttribute(): bool
    {
        $now = now()->toDateString();
        return $this->start_date <= $now && $this->end_date >= $now;
    }

    /**
     * Check if task has a book reference.
     */
    public function hasBook(): bool
    {
        return !is_null($this->book_id);
    }

    /**
     * Check if task has page range.
     */
    public function hasPageRange(): bool
    {
        return !is_null($this->page_start) && !is_null($this->page_end);
    }

    /**
     * Get task duration in days.
     */
    public function getDurationAttribute(): int
    {
        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    /**
     * Get page range display.
     */
    public function getPageRangeAttribute(): ?string
    {
        if ($this->hasPageRange()) {
            return "Pages {$this->page_start}-{$this->page_end}";
        }
        return null;
    }

    /**
     * Get task status.
     */
    public function getStatusAttribute(): string
    {
        $now = now()->toDateString();
        
        if ($this->start_date > $now) {
            return 'upcoming';
        } elseif ($this->end_date < $now) {
            return 'expired';
        } else {
            return 'active';
        }
    }

    /**
     * Generate task instances for recurring tasks.
     */
    public function generateInstances(array $userIds, ?int $teamId = null): int
    {
        if (!$this->is_recurring) {
            return 0;
        }

        $instancesCreated = 0;
        $currentDate = $this->start_date->copy();
        
        while ($currentDate <= $this->end_date) {
            $instanceEndDate = $this->calculateInstanceEndDate($currentDate);
            
            foreach ($userIds as $userId) {
                // Check if instance already exists
                $existingInstance = $this->instances()
                    ->where('user_id', $userId)
                    ->where('start_date', $currentDate->toDateString())
                    ->first();
                
                if (!$existingInstance) {
                    ProgramTaskInstance::create([
                        'program_task_id' => $this->id,
                        'user_id' => $userId,
                        'start_date' => $currentDate->toDateString(),
                        'end_date' => $instanceEndDate->toDateString(),
                        'assigned_via' => $teamId ? 'team' : 'individual',
                        'team_id' => $teamId,
                    ]);
                    $instancesCreated++;
                }
            }
            
            $currentDate = $this->getNextRecurrenceDate($currentDate);
        }
        
        return $instancesCreated;
    }

    /**
     * Calculate end date for a task instance.
     */
    private function calculateInstanceEndDate(Carbon $startDate): Carbon
    {
        return match($this->recurrence_pattern) {
            self::RECURRENCE_DAILY => $startDate->copy(),
            self::RECURRENCE_WEEKLY => $startDate->copy()->addDays(6),
            default => $startDate->copy(),
        };
    }

    /**
     * Get next recurrence date.
     */
    private function getNextRecurrenceDate(Carbon $currentDate): Carbon
    {
        return match($this->recurrence_pattern) {
            self::RECURRENCE_DAILY => $currentDate->copy()->addDay(),
            self::RECURRENCE_WEEKLY => $currentDate->copy()->addWeek(),
            default => $currentDate->copy()->addDay(),
        };
    }
}
