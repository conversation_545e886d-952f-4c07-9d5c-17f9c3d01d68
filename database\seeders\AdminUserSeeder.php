<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $systemAdminRole = Role::where('level', Role::LEVEL_SYSTEM_ADMIN)->first();

        if ($systemAdminRole) {
            User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'System Administrator',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'role_id' => $systemAdminRole->id,
                    'email_verified_at' => now(),
                ]
            );
        }
    }
}
