@extends('layouts.student')

@section('title', __('student.dashboard_title'))

@section('content')
<div class="game-container">
    <!-- Header Section -->
    <div class="mb-6">
        <div class="game-card p-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="achievement-badge bg-white text-game-blue mr-4" style="width: 60px; height: 60px; font-size: 24px;">
                        👋
                    </div>
                    <div>
                        <h1 class="text-2xl font-black">
                            {{ __('student.welcome') }}, {{ $user->name }}!
                        </h1>
                        <p class="text-blue-100 font-semibold">
                            {{ __('student.ready_to_learn') }}
                        </p>
                    </div>
                </div>
                <form method="POST" action="{{ route('student.logout') }}" class="inline">
                    @csrf
                    <button type="submit" class="text-white hover:text-blue-200 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Progress Overview -->
    <div class="mb-6">
        <div class="game-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-black text-gray-800">{{ __('student.your_progress') }}</h2>
                <div class="achievement-badge bg-game-orange" style="width: 40px; height: 40px; font-size: 16px;">
                    📊
                </div>
            </div>
            
            <!-- Level Progress -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-bold text-gray-600">{{ __('student.current_level') }}</span>
                    <span class="text-lg font-black text-game-blue">Level 5</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" data-width="75"></div>
                </div>
                <p class="text-xs text-gray-500 font-semibold mt-1">75 XP {{ __('student.until_next_level') }}</p>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card border-l-4 border-game-green">
                    <div class="achievement-badge bg-game-green mx-auto mb-2" style="width: 30px; height: 30px; font-size: 12px;">
                        📚
                    </div>
                    <p class="text-2xl font-black text-gray-800">12</p>
                    <p class="text-xs font-bold text-gray-600">{{ __('student.books_read') }}</p>
                </div>
                
                <div class="stat-card border-l-4 border-game-orange">
                    <div class="achievement-badge bg-game-orange mx-auto mb-2" style="width: 30px; height: 30px; font-size: 12px;">
                        🔥
                    </div>
                    <p class="text-2xl font-black text-gray-800">7</p>
                    <p class="text-xs font-bold text-gray-600">{{ __('student.day_streak') }}</p>
                </div>
                
                <div class="stat-card border-l-4 border-game-purple">
                    <div class="achievement-badge bg-game-purple mx-auto mb-2" style="width: 30px; height: 30px; font-size: 12px;">
                        ⭐
                    </div>
                    <p class="text-2xl font-black text-gray-800">450</p>
                    <p class="text-xs font-bold text-gray-600">{{ __('student.total_points') }}</p>
                </div>
                
                <div class="stat-card border-l-4 border-game-red">
                    <div class="achievement-badge bg-game-red mx-auto mb-2" style="width: 30px; height: 30px; font-size: 12px;">
                        🏆
                    </div>
                    <p class="text-2xl font-black text-gray-800">8</p>
                    <p class="text-xs font-bold text-gray-600">{{ __('student.achievements') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mb-6">
        <h2 class="text-xl font-black text-white mb-4">{{ __('student.quick_actions') }}</h2>
        <div class="grid grid-cols-2 gap-4">
            <button class="game-card interactive p-6 text-center" onclick="startReading()">
                <div class="achievement-badge bg-game-green mx-auto mb-3" style="width: 50px; height: 50px; font-size: 20px;">
                    📖
                </div>
                <h3 class="font-black text-gray-800 text-sm">{{ __('student.start_reading') }}</h3>
                <p class="text-xs text-gray-600 font-semibold">{{ __('student.continue_story') }}</p>
            </button>
            
            <button class="game-card interactive p-6 text-center" onclick="viewAchievements()">
                <div class="achievement-badge bg-game-orange mx-auto mb-3" style="width: 50px; height: 50px; font-size: 20px;">
                    🏅
                </div>
                <h3 class="font-black text-gray-800 text-sm">{{ __('student.achievements') }}</h3>
                <p class="text-xs text-gray-600 font-semibold">{{ __('student.view_badges') }}</p>
            </button>
            
            <button class="game-card interactive p-6 text-center" onclick="viewLeaderboard()">
                <div class="achievement-badge bg-game-purple mx-auto mb-3" style="width: 50px; height: 50px; font-size: 20px;">
                    👥
                </div>
                <h3 class="font-black text-gray-800 text-sm">{{ __('student.leaderboard') }}</h3>
                <p class="text-xs text-gray-600 font-semibold">{{ __('student.compete_friends') }}</p>
            </button>
            
            <button class="game-card interactive p-6 text-center" onclick="viewProfile()">
                <div class="achievement-badge bg-game-blue mx-auto mb-3" style="width: 50px; height: 50px; font-size: 20px;">
                    ⚙️
                </div>
                <h3 class="font-black text-gray-800 text-sm">{{ __('student.settings') }}</h3>
                <p class="text-xs text-gray-600 font-semibold">{{ __('student.customize_profile') }}</p>
            </button>
        </div>
    </div>

    <!-- Recent Achievements -->
    <div class="mb-6">
        <div class="game-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-black text-gray-800">{{ __('student.recent_achievements') }}</h2>
                <div class="achievement-badge bg-game-pink" style="width: 40px; height: 40px; font-size: 16px;">
                    🎉
                </div>
            </div>
            
            <div class="space-y-3">
                <div class="flex items-center p-3 bg-green-50 rounded-2xl border-2 border-green-200">
                    <div class="achievement-badge bg-game-green mr-3" style="width: 40px; height: 40px; font-size: 16px;">
                        📚
                    </div>
                    <div class="flex-1">
                        <h3 class="font-bold text-gray-800 text-sm">{{ __('student.bookworm_badge') }}</h3>
                        <p class="text-xs text-gray-600 font-semibold">{{ __('student.read_10_books') }}</p>
                    </div>
                    <span class="text-xs text-green-600 font-bold">{{ __('student.new') }}!</span>
                </div>
                
                <div class="flex items-center p-3 bg-orange-50 rounded-2xl border-2 border-orange-200">
                    <div class="achievement-badge bg-game-orange mr-3" style="width: 40px; height: 40px; font-size: 16px;">
                        🔥
                    </div>
                    <div class="flex-1">
                        <h3 class="font-bold text-gray-800 text-sm">{{ __('student.streak_master') }}</h3>
                        <p class="text-xs text-gray-600 font-semibold">{{ __('student.7_day_streak') }}</p>
                    </div>
                    <span class="text-xs text-orange-600 font-bold">2 {{ __('student.days_ago') }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Challenge -->
    <div class="mb-20">
        <div class="game-card p-6 bg-gradient-to-r from-purple-500 to-pink-500 text-white">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-black">{{ __('student.daily_challenge') }}</h2>
                <div class="achievement-badge bg-white text-purple-600" style="width: 40px; height: 40px; font-size: 16px;">
                    🎯
                </div>
            </div>
            
            <div class="mb-4">
                <h3 class="font-bold text-lg">{{ __('student.read_for_minutes', ['minutes' => 15]) }}</h3>
                <p class="text-purple-100 font-semibold text-sm">{{ __('student.challenge_description') }}</p>
            </div>
            
            <div class="mb-4">
                <div class="progress-bar bg-purple-400">
                    <div class="progress-fill bg-white" data-width="60"></div>
                </div>
                <p class="text-xs text-purple-100 font-semibold mt-1">9/15 {{ __('student.minutes_completed') }}</p>
            </div>
            
            <button class="game-btn bg-white text-purple-600 hover:bg-purple-50" onclick="startChallenge()">
                {{ __('student.continue_challenge') }}
            </button>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Quick action functions
    function startReading() {
        hapticFeedback('success');
        showToast('{{ __('student.starting_reading') }}', 'success');
        // Navigate to reading interface (placeholder)
        console.log('Starting reading session...');
    }
    
    function viewAchievements() {
        hapticFeedback('medium');
        showToast('{{ __('student.loading_achievements') }}', 'success');
        // Navigate to achievements page (placeholder)
        console.log('Loading achievements...');
    }
    
    function viewLeaderboard() {
        hapticFeedback('medium');
        showToast('{{ __('student.loading_leaderboard') }}', 'success');
        // Navigate to leaderboard (placeholder)
        console.log('Loading leaderboard...');
    }
    
    function viewProfile() {
        hapticFeedback('light');
        showToast('{{ __('student.loading_profile') }}', 'success');
        // Navigate to profile settings (placeholder)
        console.log('Loading profile...');
    }
    
    function startChallenge() {
        hapticFeedback('success');
        showToast('{{ __('student.challenge_started') }}', 'success');
        // Start daily challenge (placeholder)
        console.log('Starting daily challenge...');
    }
    
    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        // Simulate achievement unlock after 2 seconds
        setTimeout(() => {
            unlockAchievement(
                '{{ __('student.welcome_achievement') }}',
                '{{ __('student.first_login_today') }}'
            );
        }, 2000);
        
        // Update active navigation item
        const navItems = document.querySelectorAll('.nav-item');
        navItems[0].classList.add('active'); // Home tab
    });
</script>
@endpush
@endsection
