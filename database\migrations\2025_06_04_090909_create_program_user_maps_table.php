<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('program_user_maps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('program_id')->constrained('programs')->onDelete('cascade');
            $table->foreignId('term_user_id')->constrained('term_users')->onDelete('cascade');
            $table->integer('item_type'); // 1=item, 2=badge, 3=reward, etc.
            $table->integer('item_id'); // polymorphic reference to item, badge, reward, etc.
            $table->decimal('x_coordinate', 8, 2);
            $table->decimal('y_coordinate', 8, 2);
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('deleted_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            // Add unique constraint and indexes
            $table->unique(['program_id', 'term_user_id', 'item_type', 'item_id'], 'program_user_map_unique');
            $table->index('program_id');
            $table->index('term_user_id');
            $table->index(['item_type', 'item_id']);
            $table->index(['x_coordinate', 'y_coordinate']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('program_user_maps');
    }
};
