<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StoryCharacterStage extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'character_id',
        'stage_number',
        'name',
        'image',
        'unlock_rule_id',
        'abilities',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'stage_number' => 'integer',
            'abilities' => 'array',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get the character this stage belongs to.
     */
    public function character(): BelongsTo
    {
        return $this->belongsTo(StoryCharacter::class, 'character_id');
    }

    /**
     * Get the unlock rule for this stage.
     */
    public function unlockRule(): BelongsTo
    {
        return $this->belongsTo(StoryRule::class, 'unlock_rule_id');
    }

    /**
     * Check if stage has unlock rule.
     */
    public function hasUnlockRule(): bool
    {
        return !is_null($this->unlock_rule_id);
    }

    /**
     * Check if stage has abilities.
     */
    public function hasAbilities(): bool
    {
        return !empty($this->abilities);
    }

    /**
     * Get formatted stage name with number.
     */
    public function getFullNameAttribute(): string
    {
        return 'Stage ' . $this->stage_number . ': ' . $this->name;
    }

    /**
     * Get abilities as formatted string.
     */
    public function getAbilitiesTextAttribute(): string
    {
        if (!$this->hasAbilities()) {
            return 'No special abilities';
        }
        
        if (is_array($this->abilities)) {
            return implode(', ', $this->abilities);
        }
        
        return (string) $this->abilities;
    }

    /**
     * Check if this is the first stage.
     */
    public function isFirst(): bool
    {
        return $this->stage_number === 1;
    }

    /**
     * Check if this is the final stage for the character.
     */
    public function isFinal(): bool
    {
        return $this->stage_number === $this->character->max_stage;
    }

    /**
     * Get the next stage.
     */
    public function getNextStageAttribute(): ?StoryCharacterStage
    {
        return static::where('character_id', $this->character_id)
                    ->where('stage_number', '>', $this->stage_number)
                    ->orderBy('stage_number')
                    ->first();
    }

    /**
     * Get the previous stage.
     */
    public function getPreviousStageAttribute(): ?StoryCharacterStage
    {
        return static::where('character_id', $this->character_id)
                    ->where('stage_number', '<', $this->stage_number)
                    ->orderBy('stage_number', 'desc')
                    ->first();
    }

    /**
     * Scope to order by stage number.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('stage_number');
    }

    /**
     * Scope to get stages for a specific character.
     */
    public function scopeForCharacter($query, int $characterId)
    {
        return $query->where('character_id', $characterId);
    }

    /**
     * Scope to get stages with unlock rules.
     */
    public function scopeWithUnlockRules($query)
    {
        return $query->whereNotNull('unlock_rule_id');
    }

    /**
     * Scope to get stages without unlock rules.
     */
    public function scopeWithoutUnlockRules($query)
    {
        return $query->whereNull('unlock_rule_id');
    }

    /**
     * Scope to get first stages (stage number 1).
     */
    public function scopeFirstStages($query)
    {
        return $query->where('stage_number', 1);
    }

    /**
     * Get stage progression percentage within character.
     */
    public function getProgressionPercentageAttribute(): float
    {
        $maxStage = $this->character->max_stage;
        
        if ($maxStage === 0) {
            return 0;
        }
        
        return ($this->stage_number / $maxStage) * 100;
    }
}
