<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProgramBookQuizQuestion extends BaseModel
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'program_book_quiz_questions';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'program_book_quiz_id',
        'book_question_id',
        'question_order',
        'student_answer',
        'is_correct',
        'answered_at',
        'points_earned',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'is_correct' => 'boolean',
            'question_order' => 'integer',
            'points_earned' => 'integer',
            'answered_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the quiz this question belongs to.
     */
    public function quiz(): BelongsTo
    {
        return $this->belongsTo(ProgramBookQuiz::class, 'program_book_quiz_id');
    }

    /**
     * Get the book question.
     */
    public function bookQuestion(): BelongsTo
    {
        return $this->belongsTo(BookQuestion::class);
    }

    /**
     * Scope to get answered questions.
     */
    public function scopeAnswered($query)
    {
        return $query->whereNotNull('student_answer');
    }

    /**
     * Scope to get unanswered questions.
     */
    public function scopeUnanswered($query)
    {
        return $query->whereNull('student_answer');
    }

    /**
     * Scope to get correct answers.
     */
    public function scopeCorrect($query)
    {
        return $query->where('is_correct', true);
    }

    /**
     * Scope to get incorrect answers.
     */
    public function scopeIncorrect($query)
    {
        return $query->where('is_correct', false);
    }

    /**
     * Scope to order by question order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('question_order');
    }

    /**
     * Check if question is answered.
     */
    public function getIsAnsweredAttribute(): bool
    {
        return !is_null($this->student_answer);
    }

    /**
     * Answer the question.
     */
    public function answerQuestion(string $answer): bool
    {
        if ($this->is_answered) {
            return false; // Already answered
        }
        
        $this->student_answer = $answer;
        $this->is_correct = $this->bookQuestion->isCorrectAnswer($answer);
        $this->answered_at = now();
        
        // Calculate points
        if ($this->is_correct) {
            $this->points_earned = $this->calculatePoints();
        } else {
            $this->points_earned = 0;
        }
        
        return $this->save();
    }

    /**
     * Calculate points for this question.
     */
    protected function calculatePoints(): int
    {
        $basePoints = 10; // Base points per question
        
        // Difficulty bonus
        $difficultyMultiplier = match($this->bookQuestion->difficulty_level) {
            'easy' => 1.0,
            'medium' => 1.2,
            'hard' => 1.5,
            default => 1.0,
        };
        
        // Quiz type bonus
        $quizTypeMultiplier = match($this->quiz->quiz_type) {
            ProgramBookQuiz::TYPE_COMPLETION => 2.0,
            ProgramBookQuiz::TYPE_DAILY_READING => 1.0,
            ProgramBookQuiz::TYPE_PRACTICE => 0.5,
            default => 1.0,
        };
        
        return (int) round($basePoints * $difficultyMultiplier * $quizTypeMultiplier);
    }

    /**
     * Get question display data for quiz interface.
     */
    public function getQuestionDisplayAttribute(): array
    {
        $question = $this->bookQuestion;
        
        return [
            'id' => $this->id,
            'question_order' => $this->question_order,
            'question_text' => $question->question_text,
            'question_image_url' => $question->question_image_url,
            'question_audio_url' => $question->question_audio_url,
            'question_video_url' => $question->question_video_url,
            'answer_options' => $question->shuffled_answer_options,
            'page_range' => $question->page_range,
            'difficulty_level' => $question->difficulty_level_name,
            'has_media' => $question->hasMedia(),
            'media_type' => $question->media_type,
            'is_answered' => $this->is_answered,
            'student_answer' => $this->student_answer,
        ];
    }

    /**
     * Get question result data for review.
     */
    public function getQuestionResultAttribute(): array
    {
        $question = $this->bookQuestion;
        
        return [
            'id' => $this->id,
            'question_order' => $this->question_order,
            'question_text' => $question->question_text,
            'correct_answer' => $question->correct_answer,
            'student_answer' => $this->student_answer,
            'is_correct' => $this->is_correct,
            'points_earned' => $this->points_earned,
            'answered_at' => $this->answered_at?->format('Y-m-d H:i:s'),
            'page_range' => $question->page_range,
            'difficulty_level' => $question->difficulty_level_name,
            'explanation' => $this->getExplanation(),
        ];
    }

    /**
     * Get explanation for the answer.
     */
    protected function getExplanation(): string
    {
        if ($this->is_correct) {
            return "Correct! Well done.";
        }
        
        $explanation = "The correct answer is: {$this->bookQuestion->correct_answer}";
        
        if ($this->bookQuestion->page_range) {
            $explanation .= " (Reference: {$this->bookQuestion->page_range})";
        }
        
        return $explanation;
    }

    /**
     * Get next unanswered question in the quiz.
     */
    public function getNextUnansweredQuestion(): ?self
    {
        return $this->quiz->quizQuestions()
                         ->unanswered()
                         ->where('question_order', '>', $this->question_order)
                         ->ordered()
                         ->first();
    }

    /**
     * Get previous question in the quiz.
     */
    public function getPreviousQuestion(): ?self
    {
        return $this->quiz->quizQuestions()
                         ->where('question_order', '<', $this->question_order)
                         ->ordered()
                         ->latest('question_order')
                         ->first();
    }

    /**
     * Check if this is the last question in the quiz.
     */
    public function getIsLastQuestionAttribute(): bool
    {
        return $this->question_order === $this->quiz->total_questions;
    }

    /**
     * Check if this is the first question in the quiz.
     */
    public function getIsFirstQuestionAttribute(): bool
    {
        return $this->question_order === 1;
    }

    /**
     * Get question statistics for analytics.
     */
    public static function getQuestionStatistics(int $bookQuestionId): array
    {
        $quizQuestions = static::where('book_question_id', $bookQuestionId)
                              ->whereNotNull('student_answer');
        
        $total = $quizQuestions->count();
        $correct = $quizQuestions->where('is_correct', true)->count();
        
        return [
            'total_attempts' => $total,
            'correct_attempts' => $correct,
            'incorrect_attempts' => $total - $correct,
            'accuracy_rate' => $total > 0 ? round(($correct / $total) * 100, 2) : 0,
            'average_points' => $quizQuestions->avg('points_earned') ?? 0,
        ];
    }
}
