<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Term;
use App\Models\Role;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\Support\Attributes\Icon;


#[Icon('calendar')]
class TermResource extends BaseResource
{
    protected string $model = Term::class;

    protected string $column = 'name';

    protected array $with = ['creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.terms');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Date::make(__('admin.start_date'), 'start_date')
                ->format('d.m.Y')
                ->sortable(),
            
            Date::make(__('admin.end_date'), 'end_date')
                ->format('d.m.Y')
                ->sortable(),
            
            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
            
            Text::make(__('admin.status'), 'status')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Text::make(__('admin.name'), 'name')
                    ->required()
                    ->placeholder(__('admin.enter_name')),
                
                Flex::make([
                    Date::make(__('admin.start_date'), 'start_date')
                        ->required()
                        ->format('Y-m-d'),
                    
                    Date::make(__('admin.end_date'), 'end_date')
                        ->required()
                        ->format('Y-m-d'),
                ]),
                
                Switcher::make(__('admin.active'), 'active')
                    ->default(false),
            ]),
            
            
        ];
    }

    protected function detailFields(): iterable
    {
        return [            
                Text::make(__('admin.name'), 'name'),
                    Date::make(__('admin.start_date'), 'start_date')
                        ->format('d.m.Y'),
                    
                    Date::make(__('admin.end_date'), 'end_date')
                        ->format('d.m.Y'),
                
                    Switcher::make(__('admin.active'), 'active'),
                    Text::make(__('admin.status'), 'status'),
                Text::make(__('admin.duration_days'), 'duration'),
                HasMany::make(__('admin.term_users'), 'termUsers', 
                    resource: TermUserResource::class),
                ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['start_date' => 'desc'];
    }
}
